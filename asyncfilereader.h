#ifndef ASYNCFILEREADER_H
#define ASYNCFILEREADER_H

#include "logentry.h"
#include "logviewer_global.h"
#include <QObject>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QTextCodec>
#include <QTimer>

/**
 * @brief 异步文件读取器
 * 
 * 在后台线程中读取大文件，避免阻塞UI线程
 * 特点：
 * - 分块读取，定期发送进度更新
 * - 支持取消操作
 * - 内存使用控制
 * - 编码自动检测
 * - 错误处理和恢复
 */
class LOGVIEWER_EXPORT AsyncFileReader : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 读取状态枚举
     */
    enum ReadStatus {
        Idle,           ///< 空闲状态
        Reading,        ///< 正在读取
        Paused,         ///< 已暂停
        Cancelled,      ///< 已取消
        Completed,      ///< 已完成
        Error           ///< 错误状态
    };

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit AsyncFileReader(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~AsyncFileReader();

    /**
     * @brief 开始异步读取文件
     * @param filePath 文件路径
     * @param encoding 文件编码
     * @param maxEntries 最大读取条目数（0表示无限制）
     */
    void startReading(const QString& filePath, const QString& encoding = "UTF-8", int maxEntries = 0);
    
    /**
     * @brief 取消读取操作
     */
    void cancelReading();
    
    /**
     * @brief 暂停读取操作
     */
    void pauseReading();
    
    /**
     * @brief 恢复读取操作
     */
    void resumeReading();
    
    /**
     * @brief 获取当前状态
     * @return 读取状态
     */
    ReadStatus getStatus() const;
    
    /**
     * @brief 检查是否正在读取
     * @return 是否正在读取
     */
    bool isReading() const;
    
    /**
     * @brief 设置分块大小
     * @param chunkSize 每次读取的行数
     */
    void setChunkSize(int chunkSize);
    
    /**
     * @brief 设置进度更新间隔
     * @param intervalMs 间隔毫秒数
     */
    void setProgressUpdateInterval(int intervalMs);

signals:
    /**
     * @brief 读取开始信号
     * @param filePath 文件路径
     * @param estimatedLines 估计行数
     */
    void readingStarted(const QString& filePath, int estimatedLines);
    
    /**
     * @brief 进度更新信号
     * @param processedLines 已处理行数
     * @param totalLines 总行数（估计）
     * @param percentage 完成百分比
     */
    void progressUpdated(int processedLines, int totalLines, int percentage);
    
    /**
     * @brief 数据块读取完成信号
     * @param entries 读取到的日志条目
     * @param isLastChunk 是否为最后一块
     */
    void dataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk);
    
    /**
     * @brief 读取完成信号
     * @param totalEntries 总条目数
     * @param elapsedMs 耗时毫秒数
     */
    void readingCompleted(int totalEntries, qint64 elapsedMs);
    
    /**
     * @brief 读取取消信号
     */
    void readingCancelled();
    
    /**
     * @brief 错误信号
     * @param error 错误描述
     */
    void errorOccurred(const QString& error);

private slots:
    /**
     * @brief 执行文件读取
     */
    void performReading();
    
    /**
     * @brief 发送进度更新
     */
    void sendProgressUpdate();

private:
    /**
     * @brief 估算文件行数
     * @param filePath 文件路径
     * @return 估计行数
     */
    int estimateLineCount(const QString& filePath) const;
    
    /**
     * @brief 检测文件编码
     * @param filePath 文件路径
     * @return 检测到的编码器
     */
    QTextCodec* detectFileEncoding(const QString& filePath) const;
    
    /**
     * @brief 解析日志行
     * @param line 日志行
     * @param lineNumber 行号
     * @return 日志条目
     */
    LogEntry parseLogLine(const QString& line, int lineNumber) const;
    
    /**
     * @brief 检测日志级别
     * @param text 文本内容
     * @return 日志级别
     */
    LogEntry::LogLevel detectLogLevel(const QString& text) const;
    
    /**
     * @brief 解析时间戳
     * @param text 文本内容
     * @return 时间戳
     */
    QDateTime parseTimestamp(const QString& text) const;

private:
    // 线程相关
    QThread* m_workerThread;           ///< 工作线程
    
    // 状态控制
    mutable QMutex m_mutex;            ///< 状态互斥锁
    QWaitCondition m_pauseCondition;   ///< 暂停条件变量
    ReadStatus m_status;               ///< 当前状态
    
    // 读取参数
    QString m_filePath;                ///< 文件路径
    QString m_encoding;                ///< 文件编码
    int m_maxEntries;                  ///< 最大条目数
    int m_chunkSize;                   ///< 分块大小
    int m_progressUpdateInterval;      ///< 进度更新间隔
    
    // 进度跟踪
    QTimer* m_progressTimer;           ///< 进度更新定时器
    int m_processedLines;              ///< 已处理行数
    int m_totalLines;                  ///< 总行数（估计）
    int m_totalEntries;                ///< 总条目数
    qint64 m_startTime;                ///< 开始时间
    
    // 常量
    static const int DEFAULT_CHUNK_SIZE = 1000;           ///< 默认分块大小
    static const int DEFAULT_PROGRESS_INTERVAL = 500;     ///< 默认进度更新间隔
    static const qint64 MAX_FILE_SIZE = 500 * 1024 * 1024; ///< 最大文件大小
    static const int ENCODING_DETECTION_BYTES = 8192;     ///< 编码检测字节数
};

#endif // ASYNCFILEREADER_H
