#include "logmodelfactory.h"
#include "logentry.h"
#include <QDebug>
#include <QDateTime>
#include <QVector>

/**
 * @brief 简化的架构测试
 * 验证新的Model架构基本功能
 */
void testBasicFunctionality()
{
    qDebug() << "========== 测试基本功能 ==========";
    
    // 1. 测试工厂创建
    qDebug() << "1. 测试LogModelFactory...";
    auto circularModel = LogModelFactory::createModelForDataSource(
        LogModelFactory::Log4QtSource, 100);
    auto fileModel = LogModelFactory::createModelForDataSource(
        LogModelFactory::FileSource, 200);
    
    if (circularModel) {
        qDebug() << "✓ CircularLogModel创建成功，类型:" << circularModel->getModelType();
    } else {
        qDebug() << "✗ CircularLogModel创建失败";
        return;
    }
    
    if (fileModel) {
        qDebug() << "✓ FileLogModel创建成功，类型:" << fileModel->getModelType();
    } else {
        qDebug() << "✗ FileLogModel创建失败";
        return;
    }
    
    // 2. 测试数据添加
    qDebug() << "\n2. 测试数据添加...";
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 50; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime());
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("TestSource%1").arg(i));
        entry.setMessage(QString("测试消息 %1").arg(i));
        entry.setDetails(QString("详细信息 %1").arg(i));
        testEntries.append(entry);
    }
    
    circularModel->addLogEntries(testEntries);
    fileModel->addLogEntries(testEntries);
    
    qDebug() << "CircularModel数据量:" << circularModel->getTotalCount();
    qDebug() << "FileModel数据量:" << fileModel->getTotalCount();
    
    // 3. 测试内存使用
    qDebug() << "\n3. 测试内存使用...";
    qDebug() << "CircularModel内存使用:" << circularModel->getMemoryUsage() << "字节";
    qDebug() << "FileModel内存使用:" << fileModel->getMemoryUsage() << "字节";
    
    // 4. 测试数据获取
    qDebug() << "\n4. 测试数据获取...";
    if (circularModel->getTotalCount() > 0) {
        LogEntry entry = circularModel->getLogEntry(0);
        qDebug() << "CircularModel第一条数据:" << entry.message();
    }
    
    if (fileModel->getTotalCount() > 10) {
        QVector<LogEntry> rangeEntries = fileModel->getEntries(5, 10);
        qDebug() << "FileModel范围数据获取:" << rangeEntries.size() << "条";
    }
    
    qDebug() << "\n========== 基本功能测试完成 ==========";
}

int main()
{
    testBasicFunctionality();
    return 0;
}
