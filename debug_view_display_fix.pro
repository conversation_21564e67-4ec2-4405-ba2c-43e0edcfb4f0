QT += core gui widgets

CONFIG += c++17

TARGET = DebugViewDisplayFix
TEMPLATE = app

# 定义宏
DEFINES += SIMPLE_VERSION
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

# 包含路径
INCLUDEPATH += .

HEADERS += \
    logviewer_global.h \
    logentry.h \
    baselogmodel.h \
    filelogmodel.h \
    logsortfilterproxymodel.h \
    idatasource.h \
    simplefiledatasource.h \
    asyncfilereader_worker.h \
    simpleconfigmanager.h \
    filterexpression.h \
    compositefilterexpression.h

SOURCES += \
    debug_view_display_fix.cpp \
    logentry.cpp \
    baselogmodel.cpp \
    filelogmodel.cpp \
    logsortfilterproxymodel.cpp \
    simplefiledatasource.cpp \
    asyncfilereader_worker.cpp \
    simpleconfigmanager.cpp \
    filterexpression.cpp \
    compositefilterexpression.cpp
