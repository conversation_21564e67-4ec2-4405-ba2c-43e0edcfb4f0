#ifndef SIMPLELOGVIEWERAPPENDER_H
#define SIMPLELOGVIEWERAPPENDER_H

#include "logviewer_global.h"
#include "logentry.h"
#include <QObject>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/appenderskeleton.h>
#include <log4qt/loggingevent.h>
#endif


#ifdef LOG4QT_AVAILABLE
class LOGVIEWER_EXPORT SimpleLogViewerAppender : public Log4Qt::AppenderSkeleton
#else
class LOGVIEWER_EXPORT SimpleLogViewerAppender : public QObject
#endif
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SimpleLogViewerAppender(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SimpleLogViewerAppender() override;

    /**
     * @brief 设置是否启用
     * @param enabled 是否启用
     */
    void setEnabled(bool enabled);

    /**
     * @brief 检查是否启用
     * @return 启用状态
     */
    bool isEnabled() const { return m_enabled; }

    /**
     * @brief 获取接收到的日志条目总数
     * @return 总数
     */
    int getTotalEntryCount() const { return m_totalEntryCount; }

#ifdef LOG4QT_AVAILABLE
    // ========== Log4Qt Appender 接口实现 ==========

    /**
     * @brief Log4Qt日志事件处理方法
     * @param event Log4Qt日志事件
     */
    void append(const Log4Qt::LoggingEvent& event) override;

    /**
     * @brief 检查是否需要布局
     * @return 总是返回false，因为我们直接处理LoggingEvent
     */
    bool requiresLayout() const override;
#endif

signals:
    /**
     * @brief 新日志条目信号
     * @param entry 日志条目
     */
    void newLogEntry(const LogEntry& entry);

private:

#ifdef LOG4QT_AVAILABLE
    /**
     * @brief 将Log4Qt事件转换为LogEntry
     * @param event Log4Qt日志事件
     * @return 转换后的LogEntry
     */
    LogEntry convertToLogEntry(const Log4Qt::LoggingEvent& event) const;

    LogEntry::LogLevel convertLogLevel(int log4qtLevel) const;
#endif

private:
    bool m_enabled;             ///< 是否启用
    int m_totalEntryCount;      ///< 总接收条目数
};

#endif // SIMPLELOGVIEWERAPPENDER_H
