#include "asyncfilereader_worker.h"
#include "logentry.h"
#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <QTextStream>
#include <QFile>

class TestAsyncFileReader : public QObject
{
    Q_OBJECT

public:
    TestAsyncFileReader(QObject* parent = nullptr) : QObject(parent)
    {
        m_reader = new AsyncFileReader(this);
        
        // 连接信号
        connect(m_reader, &AsyncFileReader::readingStarted,
                this, &TestAsyncFileReader::onReadingStarted);
        connect(m_reader, &AsyncFileReader::progressUpdated,
                this, &TestAsyncFileReader::onProgressUpdated);
        connect(m_reader, &AsyncFileReader::dataChunkReady,
                this, &TestAsyncFileReader::onDataChunkReady);
        connect(m_reader, &AsyncFileReader::readingCompleted,
                this, &TestAsyncFileReader::onReadingCompleted);
        connect(m_reader, &AsyncFileReader::errorOccurred,
                this, &TestAsyncFileReader::onErrorOccurred);
    }

    void testReading(const QString& filePath)
    {
        qDebug() << "开始测试异步文件读取:" << filePath;
        m_reader->startReading(filePath, "UTF-8", 100); // 最多读取100行
    }

private slots:
    void onReadingStarted(const QString& filePath, int estimatedLines)
    {
        qDebug() << "读取开始:" << filePath << "估计行数:" << estimatedLines;
    }

    void onProgressUpdated(int processedLines, int totalLines, int percentage)
    {
        qDebug() << "进度更新:" << processedLines << "/" << totalLines << "(" << percentage << "%)";
    }

    void onDataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk)
    {
        qDebug() << "数据块就绪:" << entries.size() << "条目，最后一块:" << isLastChunk;
        
        // 显示前几条日志
        for (int i = 0; i < qMin(3, entries.size()); ++i) {
            const LogEntry& entry = entries[i];
            qDebug() << "  [" << i << "]" << entry.getTimestamp().toString("hh:mm:ss")
                     << entry.getLevelString() << entry.getMessage();
        }
    }

    void onReadingCompleted(int totalEntries, qint64 elapsedMs)
    {
        qDebug() << "读取完成:" << totalEntries << "条目，耗时:" << elapsedMs << "ms";
        
        // 测试完成，退出应用
        QTimer::singleShot(1000, qApp, &QApplication::quit);
    }

    void onErrorOccurred(const QString& error)
    {
        qDebug() << "读取错误:" << error;
        QTimer::singleShot(1000, qApp, &QApplication::quit);
    }

private:
    AsyncFileReader* m_reader;
};

// 创建测试文件
void createTestFile(const QString& filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        
        for (int i = 1; i <= 50; ++i) {
            QString timestamp = QDateTime::currentDateTime().addSecs(-i).toString("yyyy-MM-dd hh:mm:ss");
            QString level = (i % 4 == 0) ? "ERROR" : (i % 3 == 0) ? "WARN" : "INFO";
            out << timestamp << " [" << level << "] 这是测试日志消息 " << i << "\n";
        }
        file.close();
        qDebug() << "创建测试文件:" << filePath;
    }
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 创建测试文件
    QString testFilePath = "test_log.txt";
    createTestFile(testFilePath);

    // 创建测试对象
    TestAsyncFileReader test;
    
    // 延迟启动测试，确保事件循环已启动
    QTimer::singleShot(100, [&test, testFilePath]() {
        test.testReading(testFilePath);
    });

    return app.exec();
}

#include "test_asyncfilereader_worker.moc"
