#ifndef SIMPLE_TEST_H
#define SIMPLE_TEST_H

#include "simplelogviewer.h"
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QLabel>
#include <QTextEdit>
#include <QTimer>

/**
 * @brief 阶段1测试窗口类
 * 
 * 用于验证SimpleLogViewer和相关组件的基础功能
 */
class SimpleTestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SimpleTestWidget(QWidget* parent = nullptr);
    ~SimpleTestWidget() override = default;

private slots:
    void createFileViewer();
    void createLog4QtViewer();
    void testConfig();
    void clearDisplay();
    void browseFile();
    void loadFile();
    void connectLog4Qt();
    void generateTestData();
    void onDataLoaded(int count);
    void onError(const QString& error);

private:
    void setupUI();
    void connectSignals();
    void showWelcomeMessage();
    void clearViewerContainer();

private:
    // UI组件
    QPushButton* m_createFileViewerBtn;
    QPushButton* m_createLog4QtViewerBtn;
    QPushButton* m_testConfigBtn;
    QPushButton* m_clearLogBtn;
    QPushButton* m_browseBtn;
    QPushButton* m_loadFileBtn;
    QPushButton* m_connectLog4QtBtn;
    QPushButton* m_generateTestBtn;
    QPushButton* m_stopgenerateTestBtn;
    
    QLineEdit* m_encodingEdit;
    QLineEdit* m_loggerNameEdit;
    QTextEdit* m_logEdit;
    
    QWidget* m_viewerContainer;
    QVBoxLayout* m_viewerLayout;
    
    // 查看器实例
    SimpleLogViewer* m_fileViewer;
    SimpleLogViewer* m_log4qtViewer;

    QTimer *m_testDate;
};

#endif // SIMPLE_TEST_H
