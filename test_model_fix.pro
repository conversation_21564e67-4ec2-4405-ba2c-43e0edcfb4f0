QT += core

CONFIG += c++11 console
CONFIG -= app_bundle

TARGET = test_model_fix
TEMPLATE = app

HEADERS += \
    filelogmodel.h \
    baselogmodel.h \
    circularlogmodel.h \
    circularlogbuffer.h \
    logentry.h \
    logviewer_global.h

SOURCES += \
    test_model_fix.cpp \
    filelogmodel.cpp \
    baselogmodel.cpp \
    circularlogmodel.cpp \
    circularlogbuffer.cpp \
    logentry.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
