#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>

#include "filelogmodel.h"
#include "circularlogmodel.h"
#include "logentry.h"

/**
 * @brief 测试模型修复的简单程序
 */
void testFileLogModel()
{
    qDebug() << "=== 测试 FileLogModel ===";
    
    FileLogModel model;
    
    // 检查初始状态
    qDebug() << "初始状态:";
    qDebug() << "  rowCount():" << model.rowCount();
    qDebug() << "  columnCount():" << model.columnCount();
    qDebug() << "  getTotalCount():" << model.getTotalCount();
    
    // 检查列可见性
    for (int i = 0; i < static_cast<int>(BaseLogModel::ColumnCount); ++i) {
        bool visible = model.isColumnVisible(static_cast<BaseLogModel::Column>(i));
        qDebug() << "  列" << i << "可见性:" << visible;
    }
    
    // 创建测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 5; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("TestSource%1").arg(i));
        entry.setMessage(QString("测试消息 %1").arg(i));
        entry.setDetails(QString("详细信息 %1").arg(i));
        testEntries.append(entry);
    }
    
    // 添加数据
    qDebug() << "添加" << testEntries.size() << "条测试数据...";
    model.addLogEntries(testEntries);
    
    // 检查添加后的状态
    qDebug() << "添加后状态:";
    qDebug() << "  rowCount():" << model.rowCount();
    qDebug() << "  columnCount():" << model.columnCount();
    qDebug() << "  getTotalCount():" << model.getTotalCount();
    
    // 测试数据访问
    qDebug() << "测试数据访问:";
    int rows = model.rowCount();
    int cols = model.columnCount();
    
    for (int row = 0; row < qMin(3, rows); ++row) {
        qDebug() << "  行" << row << ":";
        for (int col = 0; col < cols; ++col) {
            QModelIndex index = model.index(row, col);
            QVariant data = model.data(index, Qt::DisplayRole);
            qDebug() << "    列" << col << ":" << data.toString();
        }
    }
    
    // 测试表头
    qDebug() << "测试表头:";
    for (int col = 0; col < cols; ++col) {
        QVariant header = model.headerData(col, Qt::Horizontal, Qt::DisplayRole);
        qDebug() << "  列" << col << "表头:" << header.toString();
    }
}

void testCircularLogModel()
{
    qDebug() << "\n=== 测试 CircularLogModel ===";
    
    CircularLogModel model;
    
    // 检查初始状态
    qDebug() << "初始状态:";
    qDebug() << "  rowCount():" << model.rowCount();
    qDebug() << "  columnCount():" << model.columnCount();
    qDebug() << "  getTotalCount():" << model.getTotalCount();
    
    // 创建测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 3; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("CircularSource%1").arg(i));
        entry.setMessage(QString("环形缓冲区消息 %1").arg(i));
        entry.setDetails(QString("环形缓冲区详情 %1").arg(i));
        testEntries.append(entry);
    }
    
    // 添加数据
    qDebug() << "添加" << testEntries.size() << "条测试数据...";
    model.addLogEntries(testEntries);
    
    // 检查添加后的状态
    qDebug() << "添加后状态:";
    qDebug() << "  rowCount():" << model.rowCount();
    qDebug() << "  columnCount():" << model.columnCount();
    qDebug() << "  getTotalCount():" << model.getTotalCount();
    
    // 测试数据访问
    qDebug() << "测试数据访问:";
    int rows = model.rowCount();
    int cols = model.columnCount();
    
    for (int row = 0; row < rows; ++row) {
        qDebug() << "  行" << row << ":";
        for (int col = 0; col < cols; ++col) {
            QModelIndex index = model.index(row, col);
            QVariant data = model.data(index, Qt::DisplayRole);
            qDebug() << "    列" << col << ":" << data.toString();
        }
    }
}

void testColumnVisibility()
{
    qDebug() << "\n=== 测试列可见性 ===";
    
    FileLogModel model;
    
    // 添加一些测试数据
    LogEntry entry;
    entry.setTimestamp(QDateTime::currentDateTime());
    entry.setLevel(LogEntry::LogLevel::Info);
    entry.setSource("TestSource");
    entry.setMessage("测试消息");
    entry.setDetails("测试详情");
    model.addLogEntry(entry);
    
    qDebug() << "所有列可见时:";
    qDebug() << "  columnCount():" << model.columnCount();
    
    // 隐藏详情列
    qDebug() << "隐藏详情列...";
    model.setColumnVisible(BaseLogModel::DetailsColumn, false);
    qDebug() << "  columnCount():" << model.columnCount();
    
    // 测试表头
    int cols = model.columnCount();
    for (int col = 0; col < cols; ++col) {
        QVariant header = model.headerData(col, Qt::Horizontal, Qt::DisplayRole);
        qDebug() << "  列" << col << "表头:" << header.toString();
    }
    
    // 恢复所有列
    qDebug() << "恢复所有列...";
    model.setColumnVisible(BaseLogModel::DetailsColumn, true);
    qDebug() << "  columnCount():" << model.columnCount();
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 模型修复测试程序 ===";
    
    testFileLogModel();
    testCircularLogModel();
    testColumnVisibility();
    
    qDebug() << "\n=== 测试完成 ===";
    
    return 0;
}
