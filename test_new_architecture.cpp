#include "logmodelfactory.h"
#include "circularlogmodel.h"
#include "filelogmodel.h"
#include "logentry.h"
#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>
#include <QVector>

/**
 * @brief 测试新的Model架构
 * 
 * 验证：
 * 1. LogModelFactory能正确创建不同类型的Model
 * 2. CircularLogModel和FileLogModel功能正常
 * 3. 内存管理和性能表现
 */
class ArchitectureTest
{
public:
    static void runAllTests()
    {
        qDebug() << "========== 开始测试新的Model架构 ==========";
        
        testLogModelFactory();
        testCircularLogModel();
        testFileLogModel();
        testMemoryManagement();
        testPerformance();
        
        qDebug() << "========== 所有测试完成 ==========";
    }

private:
    static void testLogModelFactory()
    {
        qDebug() << "\n--- 测试LogModelFactory ---";
        
        // 测试根据数据源类型创建Model
        auto circularModel = LogModelFactory::createModelForDataSource(
            LogModelFactory::Log4QtSource, 1000);
        auto fileModel = LogModelFactory::createModelForDataSource(
            LogModelFactory::FileSource, 5000);
        
        if (circularModel && circularModel->getModelType() == "CircularLogModel") {
            qDebug() << "✓ CircularLogModel创建成功";
        } else {
            qDebug() << "✗ CircularLogModel创建失败";
        }
        
        if (fileModel && fileModel->getModelType() == "FileLogModel") {
            qDebug() << "✓ FileLogModel创建成功";
        } else {
            qDebug() << "✗ FileLogModel创建失败";
        }
        
        // 测试自动选择
        auto autoModel = LogModelFactory::createModelAuto(50000, true, false, -1);
        if (autoModel) {
            qDebug() << "✓ 自动选择Model成功，类型:" << autoModel->getModelType();
        } else {
            qDebug() << "✗ 自动选择Model失败";
        }
    }
    
    static void testCircularLogModel()
    {
        qDebug() << "\n--- 测试CircularLogModel ---";
        
        auto model = LogModelFactory::createModel(LogModelFactory::CircularModel, 100);
        if (!model) {
            qDebug() << "✗ CircularLogModel创建失败";
            return;
        }
        
        // 测试添加数据
        QVector<LogEntry> testEntries;
        for (int i = 0; i < 150; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime());
            entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
            entry.setSource(QString("TestSource%1").arg(i));
            entry.setMessage(QString("测试消息 %1").arg(i));
            entry.setDetails(QString("详细信息 %1").arg(i));
            testEntries.append(entry);
        }
        
        model->addLogEntries(testEntries);
        
        int totalCount = model->getTotalCount();
        qDebug() << "添加150条数据后，总数:" << totalCount;
        
        if (totalCount <= 100) {
            qDebug() << "✓ CircularLogModel正确限制了数据量";
        } else {
            qDebug() << "✗ CircularLogModel未正确限制数据量";
        }
        
        // 测试内存使用
        qint64 memoryUsage = model->getMemoryUsage();
        qDebug() << "内存使用:" << memoryUsage << "字节";
        
        if (memoryUsage > 0) {
            qDebug() << "✓ 内存使用统计正常";
        } else {
            qDebug() << "✗ 内存使用统计异常";
        }
    }
    
    static void testFileLogModel()
    {
        qDebug() << "\n--- 测试FileLogModel ---";
        
        auto model = LogModelFactory::createModel(LogModelFactory::FileModel, 1000);
        if (!model) {
            qDebug() << "✗ FileLogModel创建失败";
            return;
        }
        
        // 测试添加大量数据
        QVector<LogEntry> testEntries;
        for (int i = 0; i < 500; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime());
            entry.setLevel(LogEntry::LogLevel::Info);
            entry.setSource("FileTestSource");
            entry.setMessage(QString("文件测试消息 %1").arg(i));
            entry.setDetails(QString("文件详细信息 %1").arg(i));
            testEntries.append(entry);
        }
        
        model->addLogEntries(testEntries);
        
        int totalCount = model->getTotalCount();
        qDebug() << "添加500条数据后，总数:" << totalCount;
        
        if (totalCount == 500) {
            qDebug() << "✓ FileLogModel正确保存了所有数据";
        } else {
            qDebug() << "✗ FileLogModel数据保存异常，期望500，实际" << totalCount;
        }
        
        // 测试获取数据范围
        QVector<LogEntry> rangeEntries = model->getEntries(10, 20);
        if (rangeEntries.size() == 20) {
            qDebug() << "✓ 范围数据获取正常";
        } else {
            qDebug() << "✗ 范围数据获取异常，期望20，实际" << rangeEntries.size();
        }
    }
    
    static void testMemoryManagement()
    {
        qDebug() << "\n--- 测试内存管理 ---";
        
        auto circularModel = LogModelFactory::createModel(LogModelFactory::CircularModel, 50);
        auto fileModel = LogModelFactory::createModel(LogModelFactory::FileModel, 100);
        
        // 测试内存清理
        QVector<LogEntry> largeDataSet;
        for (int i = 0; i < 200; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime());
            entry.setLevel(LogEntry::LogLevel::Warning);
            entry.setSource("MemoryTestSource");
            entry.setMessage(QString("内存测试消息 %1 - 这是一个比较长的消息用于测试内存使用").arg(i));
            entry.setDetails(QString("内存测试详细信息 %1 - 包含更多详细内容用于内存测试").arg(i));
            largeDataSet.append(entry);
        }
        
        qint64 circularMemoryBefore = circularModel->getMemoryUsage();
        qint64 fileMemoryBefore = fileModel->getMemoryUsage();
        
        circularModel->addLogEntries(largeDataSet);
        fileModel->addLogEntries(largeDataSet);
        
        qint64 circularMemoryAfter = circularModel->getMemoryUsage();
        qint64 fileMemoryAfter = fileModel->getMemoryUsage();
        
        qDebug() << "CircularModel内存使用: 前" << circularMemoryBefore 
                 << "字节, 后" << circularMemoryAfter << "字节";
        qDebug() << "FileModel内存使用: 前" << fileMemoryBefore 
                 << "字节, 后" << fileMemoryAfter << "字节";
        
        if (circularModel->getTotalCount() <= 50) {
            qDebug() << "✓ CircularModel内存管理正常";
        } else {
            qDebug() << "✗ CircularModel内存管理异常";
        }
        
        if (fileModel->getTotalCount() <= 100) {
            qDebug() << "✓ FileModel内存管理正常";
        } else {
            qDebug() << "✗ FileModel内存管理异常";
        }
    }
    
    static void testPerformance()
    {
        qDebug() << "\n--- 测试性能 ---";
        
        auto circularModel = LogModelFactory::createModel(LogModelFactory::CircularModel, 10000);
        auto fileModel = LogModelFactory::createModel(LogModelFactory::FileModel, 10000);
        
        // 准备测试数据
        QVector<LogEntry> testData;
        testData.reserve(5000);
        for (int i = 0; i < 5000; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime());
            entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
            entry.setSource(QString("PerfTestSource%1").arg(i % 10));
            entry.setMessage(QString("性能测试消息 %1").arg(i));
            entry.setDetails(QString("性能测试详细信息 %1").arg(i));
            testData.append(entry);
        }
        
        // 测试批量添加性能
        QDateTime startTime = QDateTime::currentDateTime();
        circularModel->addLogEntries(testData);
        QDateTime circularEndTime = QDateTime::currentDateTime();
        
        fileModel->addLogEntries(testData);
        QDateTime fileEndTime = QDateTime::currentDateTime();
        
        qint64 circularTime = startTime.msecsTo(circularEndTime);
        qint64 fileTime = circularEndTime.msecsTo(fileEndTime);
        
        qDebug() << "批量添加5000条数据性能:";
        qDebug() << "  CircularModel:" << circularTime << "毫秒";
        qDebug() << "  FileModel:" << fileTime << "毫秒";
        
        if (circularTime < 1000 && fileTime < 1000) {
            qDebug() << "✓ 性能测试通过";
        } else {
            qDebug() << "✗ 性能测试未通过，耗时过长";
        }
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    ArchitectureTest::runAllTests();
    
    return 0;
}
