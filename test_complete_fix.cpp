#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 完整测试修复效果的程序
 */
void testCompleteDataFlow()
{
    qDebug() << "=== 完整数据流测试 ===";
    
    // 1. 创建FileLogModel
    qDebug() << "\n1. 创建FileLogModel...";
    FileLogModel sourceModel;
    
    qDebug() << QString("源模型初始状态: rowCount=%1, columnCount=%2")
                .arg(sourceModel.rowCount())
                .arg(sourceModel.columnCount());
    
    // 2. 创建测试数据
    qDebug() << "\n2. 创建测试数据...";
    QVector<LogEntry> testEntries;
    
    // 创建不同级别的测试数据
    LogEntry::LogLevel levels[] = {
        LogEntry::LogLevel::Debug,
        LogEntry::LogLevel::Info,
        LogEntry::LogLevel::Warning,
        LogEntry::LogLevel::Error,
        LogEntry::LogLevel::Critical
    };
    
    QString messages[] = {
        "调试信息",
        "普通信息",
        "警告信息",
        "错误信息",
        "严重错误"
    };
    
    for (int i = 0; i < 10; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(levels[i % 5]);
        entry.setSource(QString("TestSource%1").arg(i));
        entry.setMessage(QString("%1 %2").arg(messages[i % 5]).arg(i));
        entry.setDetails(QString("详细信息 %1").arg(i));
        testEntries.append(entry);
        
        qDebug() << QString("  条目 %1: 级别=%2, 消息=%3")
                    .arg(i)
                    .arg(static_cast<int>(entry.level()))
                    .arg(entry.message());
    }
    
    // 3. 添加数据到源模型
    qDebug() << "\n3. 添加数据到源模型...";
    sourceModel.addLogEntries(testEntries);
    
    qDebug() << QString("源模型数据添加后: rowCount=%1, columnCount=%2, totalCount=%3")
                .arg(sourceModel.rowCount())
                .arg(sourceModel.columnCount())
                .arg(sourceModel.getTotalCount());
    
    // 4. 创建代理模型（模拟SimpleLogViewer的初始化）
    qDebug() << "\n4. 创建代理模型...";
    LogSortFilterProxyModel proxyModel;
    
    // 模拟SimpleLogViewer的级别过滤器初始化
    QSet<LogEntry::LogLevel> levelFilter;
    levelFilter.insert(LogEntry::LogLevel::Debug);
    levelFilter.insert(LogEntry::LogLevel::Info);
    levelFilter.insert(LogEntry::LogLevel::Warning);
    levelFilter.insert(LogEntry::LogLevel::Error);
    levelFilter.insert(LogEntry::LogLevel::Critical);
    
    qDebug() << "设置源模型...";
    proxyModel.setSourceModel(&sourceModel);
    
    qDebug() << "同步级别过滤器...";
    proxyModel.setLevelFilters(levelFilter);
    
    qDebug() << QString("代理模型状态: rowCount=%1, columnCount=%2")
                .arg(proxyModel.rowCount())
                .arg(proxyModel.columnCount());
    
    // 5. 验证数据显示
    qDebug() << "\n5. 验证数据显示...";
    int proxyRows = proxyModel.rowCount();
    int proxyCols = proxyModel.columnCount();
    
    if (proxyRows == 0) {
        qCritical() << "❌ 代理模型没有行数据！修复失败！";
        return;
    } else {
        qDebug() << QString("✅ 代理模型有 %1 行数据").arg(proxyRows);
    }
    
    // 显示前几行数据
    for (int row = 0; row < qMin(5, proxyRows); ++row) {
        qDebug() << QString("  代理行 %1:").arg(row);
        for (int col = 0; col < proxyCols; ++col) {
            QModelIndex proxyIndex = proxyModel.index(row, col);
            QVariant data = proxyModel.data(proxyIndex, Qt::DisplayRole);
            qDebug() << QString("    列 %1: %2").arg(col).arg(data.toString());
        }
    }
    
    // 6. 测试过滤功能
    qDebug() << "\n6. 测试过滤功能...";
    
    // 测试文本过滤
    qDebug() << "设置文本过滤器 '错误'...";
    proxyModel.setFilterPattern("错误");
    
    int filteredRows = proxyModel.rowCount();
    qDebug() << QString("文本过滤后: rowCount=%1").arg(filteredRows);
    
    if (filteredRows > 0) {
        qDebug() << "✅ 文本过滤正常工作";
        for (int row = 0; row < filteredRows; ++row) {
            QModelIndex msgIndex = proxyModel.index(row, 3); // 消息列
            QVariant msgData = proxyModel.data(msgIndex, Qt::DisplayRole);
            qDebug() << QString("  过滤后行 %1 消息: %2").arg(row).arg(msgData.toString());
        }
    } else {
        qDebug() << "⚠️ 文本过滤可能有问题";
    }
    
    // 清除文本过滤器
    qDebug() << "清除文本过滤器...";
    proxyModel.setFilterPattern("");
    qDebug() << QString("清除后: rowCount=%1").arg(proxyModel.rowCount());
    
    // 测试级别过滤
    qDebug() << "测试级别过滤 - 只显示Error和Critical...";
    proxyModel.setLevelFilter(LogEntry::LogLevel::Debug, false);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Info, false);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Warning, false);
    
    int levelFilteredRows = proxyModel.rowCount();
    qDebug() << QString("级别过滤后: rowCount=%1").arg(levelFilteredRows);
    
    if (levelFilteredRows > 0) {
        qDebug() << "✅ 级别过滤正常工作";
        for (int row = 0; row < levelFilteredRows; ++row) {
            QModelIndex levelIndex = proxyModel.index(row, 1); // 级别列
            QVariant levelData = proxyModel.data(levelIndex, Qt::DisplayRole);
            qDebug() << QString("  过滤后行 %1 级别: %2").arg(row).arg(levelData.toString());
        }
    } else {
        qDebug() << "⚠️ 级别过滤可能有问题";
    }
    
    // 恢复所有级别
    qDebug() << "恢复所有级别...";
    proxyModel.setLevelFilter(LogEntry::LogLevel::Debug, true);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Info, true);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Warning, true);
    qDebug() << QString("恢复后: rowCount=%1").arg(proxyModel.rowCount());
    
    // 7. 测试列可见性
    qDebug() << "\n7. 测试列可见性...";
    qDebug() << QString("源模型列数: %1").arg(sourceModel.columnCount());
    qDebug() << QString("代理模型列数: %1").arg(proxyModel.columnCount());
    
    // 隐藏详情列
    qDebug() << "隐藏详情列...";
    sourceModel.setColumnVisible(BaseLogModel::DetailsColumn, false);
    qDebug() << QString("隐藏后源模型列数: %1").arg(sourceModel.columnCount());
    qDebug() << QString("隐藏后代理模型列数: %1").arg(proxyModel.columnCount());
    
    // 恢复详情列
    qDebug() << "恢复详情列...";
    sourceModel.setColumnVisible(BaseLogModel::DetailsColumn, true);
    qDebug() << QString("恢复后源模型列数: %1").arg(sourceModel.columnCount());
    qDebug() << QString("恢复后代理模型列数: %1").arg(proxyModel.columnCount());
}

void testLogEntryValidity()
{
    qDebug() << "\n=== 测试LogEntry数据有效性 ===";
    
    FileLogModel model;
    
    // 创建一个测试条目
    LogEntry entry;
    entry.setTimestamp(QDateTime::currentDateTime());
    entry.setLevel(LogEntry::LogLevel::Info);
    entry.setSource("TestSource");
    entry.setMessage("测试消息");
    entry.setDetails("测试详情");
    
    model.addLogEntry(entry);
    
    // 验证通过getLogEntry获取的数据
    LogEntry retrieved = model.getLogEntry(0);
    
    qDebug() << "原始条目:";
    qDebug() << "  timestamp:" << entry.timestamp();
    qDebug() << "  level:" << static_cast<int>(entry.level());
    qDebug() << "  source:" << entry.source();
    qDebug() << "  message:" << entry.message();
    qDebug() << "  details:" << entry.details();
    
    qDebug() << "获取的条目:";
    qDebug() << "  timestamp:" << retrieved.timestamp();
    qDebug() << "  level:" << static_cast<int>(retrieved.level());
    qDebug() << "  source:" << retrieved.source();
    qDebug() << "  message:" << retrieved.message();
    qDebug() << "  details:" << retrieved.details();
    
    bool isValid = (entry.timestamp() == retrieved.timestamp() &&
                   entry.level() == retrieved.level() &&
                   entry.source() == retrieved.source() &&
                   entry.message() == retrieved.message() &&
                   entry.details() == retrieved.details());
    
    qDebug() << (isValid ? "✅ LogEntry数据完整性正常" : "❌ LogEntry数据完整性有问题");
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 完整修复验证程序 ===";
    
    testLogEntryValidity();
    testCompleteDataFlow();
    
    qDebug() << "\n=== 测试完成 ===";
    
    return 0;
}
