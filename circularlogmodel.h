#ifndef CIRCULARLOGMODEL_H
#define CIRCULARLOGMODEL_H

#include "baselogmodel.h"
#include "circularlogbuffer.h"
#include <QTimer>

/**
 * @brief 环形缓冲区日志Model
 * 
 * 专门用于实时流数据处理，如Log4Qt日志
 * 特点：
 * - 固定容量，自动覆盖旧数据
 * - 优化的UI更新策略
 * - 内存压力管理
 * - 高性能的批量更新
 */
class LOGVIEWER_EXPORT CircularLogModel : public BaseLogModel
{
    Q_OBJECT

public:
    explicit CircularLogModel(int capacity = 20000, QObject* parent = nullptr);
    ~CircularLogModel() override;

    // ========== QAbstractTableModel实现 ==========
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;

    // ========== BaseLogModel实现 ==========
    void addLogEntry(const LogEntry& entry) override;
    void addLogEntries(const QVector<LogEntry>& entries) override;
    LogEntry getLogEntry(int row) const override;
    QVector<LogEntry> getEntries(int startIndex, int count) const override;
    void clear() override;
    qint64 getMemoryUsage() const override;
    int getTotalCount() const override;
    QString getModelType() const override { return "CircularLogModel"; }

    // ========== 环形缓冲区特有功能 ==========
    
    /**
     * @brief 获取缓冲区容量
     */
    int getCapacity() const;
    
    /**
     * @brief 设置缓冲区容量
     */
    void setCapacity(int capacity);
    
    /**
     * @brief 设置最大条目数（兼容性方法）
     */
    void setMaxEntries(uint64_t maxEntries);
    
    /**
     * @brief 检查是否需要内存清理
     */
    bool needsMemoryCleanup() const;
    
    /**
     * @brief 强制内存清理
     */
    void forceMemoryCleanup();
    
    /**
     * @brief 获取缓冲区使用率
     */
    double getUsageRatio() const;
    
    /**
     * @brief 是否已满
     */
    bool isFull() const;
    
    /**
     * @brief 删除指定范围的条目（环形缓冲区不支持）
     */
    void removeEntriesAt(int startIndex, int count);
    
    /**
     * @brief 保留指定范围的条目（环形缓冲区不支持）
     */
    void retainDataRange(int startIndex, int endIndex, int marginCount);

private slots:
    /**
     * @brief 执行延迟的UI更新
     */
    void performScheduledUIUpdate();

private:
    CircularLogBuffer m_entries;        ///< 环形缓冲区
    uint64_t m_maxEntries;              ///< 最大条目数
    
    // UI更新优化
    QTimer* m_uiUpdateTimer;            ///< UI更新定时器
    bool m_pendingUIUpdate;             ///< 是否有待处理的UI更新
    int m_lastUpdateSize;               ///< 上次更新时的大小
    
    /**
     * @brief 安排UI更新（延迟批量更新）
     */
    void scheduleUIUpdate();
    
    /**
     * @brief 检查是否应该使用延迟更新
     */
    bool shouldUseDelayedUpdate(int newEntriesCount) const;
    
    /**
     * @brief 格式化日志条目数据（重写基类方法以适配LogEntry接口）
     */
    QVariant formatCircularLogEntryData(const LogEntry& entry, int column, int role) const;
};

#endif // CIRCULARLOGMODEL_H
