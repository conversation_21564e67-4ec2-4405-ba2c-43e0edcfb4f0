#include "simple_async_file_reader.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDebug>
#include <QMutexLocker>
#include <QMetaObject>

SimpleAsyncFileReader::SimpleAsyncFileReader(QObject* parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_status(Idle)
    , m_maxLines(0)
    , m_chunkSize(1000)
    , m_processedLines(0)
    , m_totalLines(0)
    , m_startTime(0)
{
    // 创建工作线程
    m_workerThread = new QThread(this);
    
    // 将当前对象移动到工作线程
    moveToThread(m_workerThread);
    
    // 启动线程
    m_workerThread->start();
    
    qDebug() << "SimpleAsyncFileReader created";
}

SimpleAsyncFileReader::~SimpleAsyncFileReader()
{
    if (m_workerThread && m_workerThread->isRunning()) {
        cancelReading();
        m_workerThread->quit();
        m_workerThread->wait(3000);
    }
    qDebug() << "SimpleAsyncFileReader destroyed";
}

void SimpleAsyncFileReader::startReading(const QString& filePath, int maxLines)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status == Reading) {
        qWarning() << "Already reading a file";
        return;
    }
    
    // 检查文件
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        emit errorOccurred(QString("文件不存在: %1").arg(filePath));
        return;
    }
    
    if (!fileInfo.isReadable()) {
        emit errorOccurred(QString("文件不可读: %1").arg(filePath));
        return;
    }
    
    // 文件大小限制 500MB
    if (fileInfo.size() > 500 * 1024 * 1024) {
        emit errorOccurred(QString("文件过大: %1 MB").arg(fileInfo.size() / 1024 / 1024));
        return;
    }
    
    // 设置参数
    m_filePath = filePath;
    m_maxLines = maxLines;
    m_processedLines = 0;
    m_status = Reading;
    
    // 估算行数
    m_totalLines = estimateLineCount(filePath);
    m_startTime = QDateTime::currentMSecsSinceEpoch();
    
    locker.unlock();
    
    // 发送开始信号
    emit readingStarted(filePath, m_totalLines);
    
    // 在工作线程中执行读取
    QMetaObject::invokeMethod(this, "doReading", Qt::QueuedConnection);
    
    qDebug() << "Started reading file:" << filePath;
}

void SimpleAsyncFileReader::cancelReading()
{
    QMutexLocker locker(&m_mutex);
    if (m_status == Reading) {
        m_status = Cancelled;
        emit readingCancelled();
        qDebug() << "Reading cancelled";
    }
}

SimpleAsyncFileReader::Status SimpleAsyncFileReader::getStatus() const
{
    QMutexLocker locker(&m_mutex);
    return m_status;
}

void SimpleAsyncFileReader::setChunkSize(int size)
{
    QMutexLocker locker(&m_mutex);
    m_chunkSize = qMax(100, size); // 最小100行
}

void SimpleAsyncFileReader::doReading()
{
    QFile file(m_filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit errorOccurred(QString("无法打开文件: %1").arg(file.errorString()));
        return;
    }
    
    QTextStream stream(&file);
    stream.setCodec("UTF-8"); // 简化：固定使用UTF-8
    
    QStringList chunkLines;
    chunkLines.reserve(m_chunkSize);
    
    int lineNumber = 0;
    
    while (!stream.atEnd()) {
        // 检查是否被取消
        {
            QMutexLocker locker(&m_mutex);
            if (m_status == Cancelled) {
                file.close();
                return;
            }
        }
        
        QString line = stream.readLine();
        lineNumber++;
        
        // 跳过空行
        if (line.trimmed().isEmpty()) {
            continue;
        }
        
        chunkLines.append(line);
        
        // 更新进度
        {
            QMutexLocker locker(&m_mutex);
            m_processedLines = lineNumber;
        }
        
        // 发送进度更新（每100行更新一次）
        if (lineNumber % 100 == 0) {
            int percentage = 0;
            if (m_totalLines > 0) {
                percentage = qMin(100, (lineNumber * 100) / m_totalLines);
            }
            emit progressUpdated(lineNumber, m_totalLines, percentage);
        }
        
        // 检查是否达到分块大小或最大行数
        if (chunkLines.size() >= m_chunkSize || 
            (m_maxLines > 0 && lineNumber >= m_maxLines)) {
            
            bool isLastChunk = (m_maxLines > 0 && lineNumber >= m_maxLines) || stream.atEnd();
            emit dataChunkReady(chunkLines, isLastChunk);
            
            chunkLines.clear();
            chunkLines.reserve(m_chunkSize);
            
            if (isLastChunk) {
                break;
            }
        }
    }
    
    // 发送剩余数据
    if (!chunkLines.isEmpty()) {
        emit dataChunkReady(chunkLines, true);
    }
    
    file.close();
    
    // 更新状态
    {
        QMutexLocker locker(&m_mutex);
        m_status = Completed;
    }
    
    // 计算耗时
    qint64 elapsedMs = QDateTime::currentMSecsSinceEpoch() - m_startTime;
    
    emit readingCompleted(lineNumber, elapsedMs);
    
    qDebug() << "Reading completed:" << lineNumber << "lines in" << elapsedMs << "ms";
}

int SimpleAsyncFileReader::estimateLineCount(const QString& filePath) const
{
    QFileInfo fileInfo(filePath);
    qint64 fileSize = fileInfo.size();
    
    if (fileSize <= 0) {
        return 0;
    }
    
    // 估算：平均每行80字节
    return static_cast<int>(fileSize / 80);
}

#include "simple_async_file_reader.moc"
