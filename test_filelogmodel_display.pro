QT += core widgets

CONFIG += c++11

TARGET = test_filelogmodel_display
TEMPLATE = app

HEADERS += \
    filelogmodel.h \
    baselogmodel.h \
    logentry.h \
    logviewer_global.h

SOURCES += \
    test_filelogmodel_display.cpp \
    filelogmodel.cpp \
    baselogmodel.cpp \
    logentry.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
