#include "streamlined_log_appender.h"
#include <QDateTime>
#include <QDebug>
#include <QMetaObject>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/level.h>
#include <log4qt/logger.h>
#endif

StreamlinedLogAppender::StreamlinedLogAppender(QObject* parent)
#ifdef LOG4QT_AVAILABLE
    : Log4Qt::AppenderSkeleton(parent)
#else
    : QObject(parent)
#endif
    , m_totalCount(0)
{
    // 注意：不能在这里使用qDebug()，可能会形成递归循环
    // 如果需要调试，可以写入文件或使用其他方式
}

StreamlinedLogAppender::~StreamlinedLogAppender()
{
    // 注意：不能在这里使用qDebug()，可能会形成递归循环
    // 如果需要调试，可以写入文件或使用其他方式
}

#ifdef LOG4QT_AVAILABLE
void StreamlinedLogAppender::append(const Log4Qt::LoggingEvent& event)
{
    // 注意：不能在这里使用qDebug()，会形成递归循环！
    // qDebug() -> Log4Qt -> append() -> qDebug() -> 无限循环

    // 转换Log4Qt事件为LogEntry
    LogEntry entry = convertToLogEntry(event);

    // 更新计数
    m_totalCount++;

    // 线程安全的信号发射
    emitLogReceived(entry);
}

void StreamlinedLogAppender::activateOptions()
{
    // 调用父类的activateOptions
    Log4Qt::AppenderSkeleton::activateOptions();

    // 注意：不能在这里使用qDebug()，会形成递归循环！
    // 如果需要调试，可以写入文件或使用其他方式

    // 这里可以添加任何需要的初始化代码
    // 对于我们的简单实现，调用父类方法就足够了
}

LogEntry StreamlinedLogAppender::convertToLogEntry(const Log4Qt::LoggingEvent& event) const
{
    // 时间戳
    QDateTime timestamp = QDateTime::fromMSecsSinceEpoch(event.timeStamp());
    
    // 日志级别
    LogEntry::LogLevel level = convertLogLevel(event.level().toInt());
    
    // 来源
    QString source = "Unknown";
    if (event.logger()) {
        source = event.logger()->name();
    }
    
    // 消息
    QString message = event.message();
    
    // 详细信息（简化版）
    QString details;
    if (!event.ndc().isEmpty()) {
        details += QString("NDC: %1\n").arg(event.ndc());
    }
    
    // 线程信息
    if (!event.threadName().isEmpty()) {
        details += QString("线程: %1\n").arg(event.threadName());
    }
    
    return LogEntry(timestamp, level, source, message, details);
}

LogEntry::LogLevel StreamlinedLogAppender::convertLogLevel(int log4qtLevel) const
{
    // Log4Qt标准级别到LogEntry级别的映射
    switch (log4qtLevel) {
        case 5000:  // TRACE_INT
            return LogEntry::LogLevel::Debug;  // 将TRACE映射为Debug
        case 10000: // DEBUG_INT
            return LogEntry::LogLevel::Debug;
        case 20000: // INFO_INT
            return LogEntry::LogLevel::Info;
        case 30000: // WARN_INT
            return LogEntry::LogLevel::Warning;
        case 40000: // ERROR_INT
            return LogEntry::LogLevel::Error;
        case 50000: // FATAL_INT
            return LogEntry::LogLevel::Critical;
        default:
            // 注意：不能在这里使用qDebug()，会形成递归循环
            // 未知级别默认使用Info级别
            return LogEntry::LogLevel::Info;
    }
}
#endif

void StreamlinedLogAppender::simulateLogEntry(const LogEntry& entry)
{
    m_totalCount++;
    emitLogReceived(entry);
}

void StreamlinedLogAppender::emitLogReceived(const LogEntry& entry)
{
    // 使用Qt::QueuedConnection确保线程安全
    // 如果当前在主线程，直接发射；如果在其他线程，排队到主线程
    QMetaObject::invokeMethod(this, [this, entry]() {
        emit logReceived(entry);
    }, Qt::QueuedConnection);
}
