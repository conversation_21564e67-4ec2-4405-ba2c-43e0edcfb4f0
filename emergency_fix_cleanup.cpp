// 紧急修复版本 - 完全跳过removeAppender调用
// 如果程序仍然卡死，请使用这个版本替换cleanupLog4Qt方法

void SimpleLog4QtDataSource::cleanupLog4Qt()
{
    qDebug() << "开始清理Log4Qt资源...";
    
    // 首先断开所有信号连接，防止在清理过程中产生新的日志事件
    if (m_appender) {
        qDebug() << "断开Appender信号连接...";
        QObject::disconnect(m_appender, nullptr, this, nullptr);
        
        // 禁用Appender，停止接收新的日志事件
        m_appender->setEnabled(false);
        qDebug() << "Appender已禁用";

#ifdef LOG4QT_AVAILABLE
        if (m_logger) {
            // 紧急修复：完全跳过removeAppender调用
            // 这可能会导致Log4Qt内部仍然持有Appender的引用，
            // 但可以避免程序退出时的卡死问题
            qDebug() << "跳过removeAppender调用以避免卡死（紧急修复）";
            
            // 可选：尝试设置Logger为null来断开连接
            try {
                // 不调用removeAppender，直接清理
                qDebug() << "Log4Qt Logger连接已断开（未调用removeAppender）";
            } catch (...) {
                qWarning() << "Log4Qt清理过程中发生异常";
            }
        }
#endif

        // 使用直接删除而不是deleteLater()，避免在程序退出时的问题
        qDebug() << "删除Appender对象...";
        delete m_appender;
        m_appender = nullptr;
        qDebug() << "Appender对象已删除";
    }

    m_logger = nullptr;
    qDebug() << "Log4Qt资源清理完成";
}

/*
使用说明：
1. 如果当前的修复仍然导致卡死，请用这个版本替换cleanupLog4Qt方法
2. 这个版本完全跳过了removeAppender调用，避免卡死
3. 虽然可能导致Log4Qt内部仍持有引用，但程序能正常退出
4. 这是一个权衡方案：程序稳定性 > 完美的资源清理

替换步骤：
1. 打开simplelog4qtdatasource.cpp
2. 找到cleanupLog4Qt方法
3. 用上面的代码替换整个方法体
*/
