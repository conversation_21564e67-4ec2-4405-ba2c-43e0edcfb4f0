#include "asyncfilereader.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QRegularExpression>
#include <QDateTime>
#include <QDebug>
#include <QElapsedTimer>
#include <QMutexLocker>

// 常量定义
const int AsyncFileReader::DEFAULT_CHUNK_SIZE;
const int AsyncFileReader::DEFAULT_PROGRESS_INTERVAL;
const qint64 AsyncFileReader::MAX_FILE_SIZE;
const int AsyncFileReader::ENCODING_DETECTION_BYTES;

AsyncFileReader::AsyncFileReader(QObject* parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_status(Idle)
    , m_maxEntries(0)
    , m_chunkSize(DEFAULT_CHUNK_SIZE)
    , m_progressUpdateInterval(DEFAULT_PROGRESS_INTERVAL)
    , m_processedLines(0)
    , m_totalLines(0)
    , m_totalEntries(0)
    , m_startTime(0)
{
    // 创建工作线程
    m_workerThread = new QThread(this);
    
    // 创建进度更新定时器
    m_progressTimer = new QTimer(this);
    m_progressTimer->setInterval(m_progressUpdateInterval);
    connect(m_progressTimer, &QTimer::timeout, this, &AsyncFileReader::sendProgressUpdate);
    
    qDebug() << "AsyncFileReader created";
}

AsyncFileReader::~AsyncFileReader()
{
    // 确保线程正确停止
    if (m_workerThread && m_workerThread->isRunning()) {
        cancelReading();
        m_workerThread->quit();
        m_workerThread->wait(3000); // 等待最多3秒
    }
    
    qDebug() << "AsyncFileReader destroyed";
}

void AsyncFileReader::startReading(const QString& filePath, const QString& encoding, int maxEntries)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status == Reading) {
        qWarning() << "Already reading a file";
        return;
    }
    
    // 检查文件
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        emit errorOccurred(QString("文件不存在: %1").arg(filePath));
        return;
    }
    
    if (!fileInfo.isReadable()) {
        emit errorOccurred(QString("文件不可读: %1").arg(filePath));
        return;
    }
    
    if (fileInfo.size() > MAX_FILE_SIZE) {
        emit errorOccurred(QString("文件过大: %1 MB，超过限制 %2 MB")
                          .arg(fileInfo.size() / 1024 / 1024)
                          .arg(MAX_FILE_SIZE / 1024 / 1024));
        return;
    }
    
    // 设置参数
    m_filePath = filePath;
    m_encoding = encoding;
    m_maxEntries = maxEntries;
    m_processedLines = 0;
    m_totalEntries = 0;
    m_status = Reading;
    
    // 估算行数
    m_totalLines = estimateLineCount(filePath);
    
    locker.unlock();
    
    // 发送开始信号
    emit readingStarted(filePath, m_totalLines);
    
    // 启动进度定时器
    m_progressTimer->start();
    
    // 记录开始时间
    m_startTime = QDateTime::currentMSecsSinceEpoch();
    
    // 在工作线程中执行读取
    QMetaObject::invokeMethod(this, "performReading", Qt::QueuedConnection);
    
    qDebug() << "Started reading file:" << filePath << "estimated lines:" << m_totalLines;
}

void AsyncFileReader::cancelReading()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status == Reading || m_status == Paused) {
        m_status = Cancelled;
        m_pauseCondition.wakeAll(); // 唤醒可能暂停的线程
        
        // 停止进度定时器
        m_progressTimer->stop();
        
        locker.unlock();
        emit readingCancelled();
        
        qDebug() << "Reading cancelled";
    }
}

void AsyncFileReader::pauseReading()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status == Reading) {
        m_status = Paused;
        m_progressTimer->stop();
        
        qDebug() << "Reading paused";
    }
}

void AsyncFileReader::resumeReading()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status == Paused) {
        m_status = Reading;
        m_pauseCondition.wakeAll();
        m_progressTimer->start();
        
        qDebug() << "Reading resumed";
    }
}

AsyncFileReader::ReadStatus AsyncFileReader::getStatus() const
{
    QMutexLocker locker(&m_mutex);
    return m_status;
}

bool AsyncFileReader::isReading() const
{
    QMutexLocker locker(&m_mutex);
    return m_status == Reading;
}

void AsyncFileReader::setChunkSize(int chunkSize)
{
    QMutexLocker locker(&m_mutex);
    m_chunkSize = qMax(100, chunkSize); // 最小100行
}

void AsyncFileReader::setProgressUpdateInterval(int intervalMs)
{
    QMutexLocker locker(&m_mutex);
    m_progressUpdateInterval = qMax(100, intervalMs); // 最小100ms
    m_progressTimer->setInterval(m_progressUpdateInterval);
}

void AsyncFileReader::performReading()
{
    QFile file(m_filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit errorOccurred(QString("无法打开文件: %1").arg(file.errorString()));
        return;
    }
    
    // 检测编码
    QTextCodec* codec = detectFileEncoding(m_filePath);
    if (!codec) {
        codec = QTextCodec::codecForName(m_encoding.toUtf8());
        if (!codec) {
            codec = QTextCodec::codecForName("UTF-8");
        }
    }
    
    QTextStream stream(&file);
    stream.setCodec(codec);
    
    QVector<LogEntry> chunkEntries;
    chunkEntries.reserve(m_chunkSize);
    
    int lineNumber = 0;
    QString line;
    
    while (!stream.atEnd()) {
        // 检查状态
        {
            QMutexLocker locker(&m_mutex);
            
            // 处理暂停
            while (m_status == Paused) {
                m_pauseCondition.wait(&m_mutex);
            }
            
            // 检查取消
            if (m_status == Cancelled) {
                file.close();
                return;
            }
        }
        
        line = stream.readLine();
        lineNumber++;
        
        if (line.trimmed().isEmpty()) {
            continue; // 跳过空行
        }
        
        // 解析日志行
        LogEntry entry = parseLogLine(line, lineNumber);
        chunkEntries.append(entry);
        
        // 更新处理行数
        {
            QMutexLocker locker(&m_mutex);
            m_processedLines = lineNumber;
            m_totalEntries++;
        }
        
        // 检查是否达到分块大小或最大条目数
        if (chunkEntries.size() >= m_chunkSize || 
            (m_maxEntries > 0 && m_totalEntries >= m_maxEntries)) {
            
            bool isLastChunk = (m_maxEntries > 0 && m_totalEntries >= m_maxEntries) || stream.atEnd();
            emit dataChunkReady(chunkEntries, isLastChunk);
            
            chunkEntries.clear();
            chunkEntries.reserve(m_chunkSize);
            
            if (isLastChunk) {
                break;
            }
        }
    }
    
    // 发送剩余数据
    if (!chunkEntries.isEmpty()) {
        emit dataChunkReady(chunkEntries, true);
    }
    
    file.close();
    
    // 停止进度定时器
    m_progressTimer->stop();
    
    // 更新状态
    {
        QMutexLocker locker(&m_mutex);
        m_status = Completed;
    }
    
    // 计算耗时
    qint64 elapsedMs = QDateTime::currentMSecsSinceEpoch() - m_startTime;
    
    emit readingCompleted(m_totalEntries, elapsedMs);
    
    qDebug() << "Reading completed:" << m_totalEntries << "entries in" << elapsedMs << "ms";
}

void AsyncFileReader::sendProgressUpdate()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status != Reading) {
        return;
    }
    
    int percentage = 0;
    if (m_totalLines > 0) {
        percentage = qMin(100, (m_processedLines * 100) / m_totalLines);
    }
    
    emit progressUpdated(m_processedLines, m_totalLines, percentage);
}

int AsyncFileReader::estimateLineCount(const QString& filePath) const
{
    QFileInfo fileInfo(filePath);
    qint64 fileSize = fileInfo.size();

    if (fileSize <= 0) {
        return 0;
    }

    // 估算：平均每行100字节
    return static_cast<int>(fileSize / 100);
}

QTextCodec* AsyncFileReader::detectFileEncoding(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return nullptr;
    }

    // 读取文件开头用于编码检测
    QByteArray data = file.read(ENCODING_DETECTION_BYTES);
    file.close();

    if (data.isEmpty()) {
        return nullptr;
    }

    // 检查BOM
    if (data.startsWith("\xEF\xBB\xBF")) {
        qDebug() << "Detected UTF-8 BOM";
        return QTextCodec::codecForName("UTF-8");
    }

    if (data.startsWith("\xFF\xFE") || data.startsWith("\xFE\xFF")) {
        qDebug() << "Detected UTF-16 BOM";
        return QTextCodec::codecForName("UTF-16");
    }

    // 尝试UTF-8解码
    QTextCodec* utf8Codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::ConverterState state;
    QString utf8Text = utf8Codec->toUnicode(data.constData(), data.size(), &state);

    // 检查UTF-8解码是否成功且包含有效字符
    if (state.invalidChars == 0) {
        // 进一步检查是否包含有效的Unicode字符
        bool hasValidUnicode = false;
        for (const QChar& ch : utf8Text) {
            if (ch.unicode() >= 0x80) { // 非ASCII字符
                hasValidUnicode = true;
                break;
            }
        }

        if (hasValidUnicode) {
            qDebug() << "Detected UTF-8 encoding";
            return utf8Codec;
        }
    }

    // 尝试GBK解码
    QTextCodec* gbkCodec = QTextCodec::codecForName("GBK");
    if (gbkCodec) {
        QTextCodec::ConverterState gbkState;
        QString gbkText = gbkCodec->toUnicode(data.constData(), data.size(), &gbkState);

        if (gbkState.invalidChars == 0) {
            // 检查是否包含中文字符
            for (const QChar& ch : gbkText) {
                if (ch.unicode() >= 0x4E00 && ch.unicode() <= 0x9FFF) {
                    qDebug() << "Detected GBK encoding";
                    return gbkCodec;
                }
            }
        }
    }

    // 默认返回UTF-8
    qDebug() << "Using default UTF-8 encoding";
    return utf8Codec;
}

LogEntry AsyncFileReader::parseLogLine(const QString& line, int lineNumber) const
{
    // 尝试解析时间戳
    QDateTime timestamp = parseTimestamp(line);

    // 检测日志级别
    LogEntry::LogLevel level = detectLogLevel(line);

    // 构建源信息
    QString source = QString("File:%1").arg(lineNumber);

    // 消息内容（去除时间戳和级别信息后的内容）
    QString message = line.trimmed();

    return LogEntry(timestamp, level, source, message, "");
}

LogEntry::LogLevel AsyncFileReader::detectLogLevel(const QString& text) const
{
    QString upperText = text.toUpper();

    if (upperText.contains("ERROR") || upperText.contains("错误")) {
        return LogEntry::LogLevel::Error;
    } else if (upperText.contains("WARN") || upperText.contains("警告")) {
        return LogEntry::LogLevel::Warning;
    } else if (upperText.contains("INFO") || upperText.contains("信息")) {
        return LogEntry::LogLevel::Info;
    } else if (upperText.contains("DEBUG") || upperText.contains("调试")) {
        return LogEntry::LogLevel::Debug;
    }
    return LogEntry::LogLevel::Info; // 默认级别
}

QDateTime AsyncFileReader::parseTimestamp(const QString& text) const
{
    // 常见的时间戳格式
    QStringList patterns = {
        "yyyy-MM-dd hh:mm:ss.zzz",
        "yyyy-MM-dd hh:mm:ss",
        "yyyy/MM/dd hh:mm:ss",
        "MM-dd hh:mm:ss",
        "hh:mm:ss.zzz",
        "hh:mm:ss"
    };

    // 尝试匹配各种时间戳格式
    QRegularExpression timeRegex(R"(\d{1,4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}(?:\.\d{1,3})?|\d{1,2}:\d{1,2}:\d{1,2}(?:\.\d{1,3})?)");
    QRegularExpressionMatch match = timeRegex.match(text);

    if (match.hasMatch()) {
        QString timeStr = match.captured(0);

        for (const QString& pattern : patterns) {
            QDateTime dateTime = QDateTime::fromString(timeStr, pattern);
            if (dateTime.isValid()) {
                // 如果只有时间没有日期，使用今天的日期
                if (!timeStr.contains('-') && !timeStr.contains('/')) {
                    QDate today = QDate::currentDate();
                    dateTime.setDate(today);
                }
                return dateTime;
            }
        }
    }

    // 如果无法解析时间戳，返回当前时间
    return QDateTime::currentDateTime();
}
