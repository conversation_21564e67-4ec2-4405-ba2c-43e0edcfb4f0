#include "logmodelfactory.h"
#include "circularlogmodel.h"
#include "filelogmodel.h"
#include <QDebug>

std::unique_ptr<BaseLogModel> LogModelFactory::createModel(ModelType type, int capacity)
{
    int validatedCapacity = validateCapacity(capacity, type);
    
    switch (type) {
        case CircularModel:
            return createCircularModel(validatedCapacity);
        case FileModel:
            return createFileModel(validatedCapacity);
        default:
            qWarning() << "LogModelFactory: 未知的Model类型:" << type;
            return createCircularModel(validatedCapacity); // 默认返回CircularModel
    }
}

std::unique_ptr<BaseLogModel> LogModelFactory::createModelForDataSource(const QString& sourceType, int capacity)
{
    DataSourceType parsedType = parseDataSourceType(sourceType);
    return createModelForDataSource(parsedType, capacity);
}

std::unique_ptr<BaseLogModel> LogModelFactory::createModelForDataSource(DataSourceType sourceType, int capacity)
{
    ModelType recommendedType = getRecommendedModelType(sourceType);
    
    qDebug() << "LogModelFactory: 为数据源类型" << getDataSourceTypeName(sourceType) 
             << "创建" << getModelTypeName(recommendedType);
    
    return createModel(recommendedType, capacity);
}

std::unique_ptr<BaseLogModel> LogModelFactory::createModelForScenario(bool isRealTimeStream, int capacity)
{
    ModelType type = isRealTimeStream ? CircularModel : FileModel;
    
    qDebug() << "LogModelFactory: 为" << (isRealTimeStream ? "实时流" : "文件") 
             << "场景创建" << getModelTypeName(type);
    
    return createModel(type, capacity);
}

std::unique_ptr<BaseLogModel> LogModelFactory::createModelAuto(
    int expectedDataSize, 
    bool isRealTime, 
    bool needsFullHistory, 
    int capacity)
{
    ModelType type;
    
    // 自动选择逻辑
    if (isRealTime && !needsFullHistory) {
        // 实时数据且不需要完整历史 -> CircularModel
        type = CircularModel;
    } else if (needsFullHistory || expectedDataSize > 100000) {
        // 需要完整历史或大数据集 -> FileModel
        type = FileModel;
    } else {
        // 其他情况根据数据大小决定
        type = (expectedDataSize > 50000) ? FileModel : CircularModel;
    }
    
    qDebug() << "LogModelFactory: 自动选择" << getModelTypeName(type) 
             << "，预期数据大小:" << expectedDataSize 
             << "，实时:" << isRealTime 
             << "，需要完整历史:" << needsFullHistory;
    
    return createModel(type, capacity);
}

LogModelFactory::DataSourceType LogModelFactory::parseDataSourceType(const QString& sourceTypeString)
{
    QString lowerType = sourceTypeString.toLower();
    
    if (lowerType.contains("log4qt") || lowerType.contains("实时")) {
        return Log4QtSource;
    } else if (lowerType.contains("file") || lowerType.contains("文件")) {
        return FileSource;
    } else if (lowerType.contains("network") || lowerType.contains("网络")) {
        return NetworkSource;
    } else if (lowerType.contains("database") || lowerType.contains("数据库")) {
        return DatabaseSource;
    } else {
        qDebug() << "LogModelFactory: 无法识别数据源类型:" << sourceTypeString;
        return UnknownSource;
    }
}

LogModelFactory::ModelType LogModelFactory::getRecommendedModelType(DataSourceType sourceType)
{
    switch (sourceType) {
        case Log4QtSource:
        case NetworkSource:
            // 实时流数据推荐使用CircularModel
            return CircularModel;
            
        case FileSource:
        case DatabaseSource:
            // 文件和数据库数据推荐使用FileModel
            return FileModel;
            
        case UnknownSource:
        default:
            // 未知类型默认使用CircularModel
            qDebug() << "LogModelFactory: 未知数据源类型，使用默认CircularModel";
            return CircularModel;
    }
}

int LogModelFactory::getDefaultCapacity(ModelType type)
{
    switch (type) {
        case CircularModel:
            return 20000;   // 环形缓冲区默认2万条
        case FileModel:
            return 1000000; // 文件模型默认100万条
        default:
            return 20000;
    }
}

QString LogModelFactory::getModelTypeName(ModelType type)
{
    switch (type) {
        case CircularModel:
            return "CircularLogModel";
        case FileModel:
            return "FileLogModel";
        default:
            return "UnknownModel";
    }
}

QString LogModelFactory::getDataSourceTypeName(DataSourceType sourceType)
{
    switch (sourceType) {
        case Log4QtSource:
            return "Log4Qt实时日志源";
        case FileSource:
            return "文件日志源";
        case NetworkSource:
            return "网络日志源";
        case DatabaseSource:
            return "数据库日志源";
        case UnknownSource:
        default:
            return "未知日志源";
    }
}

int LogModelFactory::validateCapacity(int capacity, ModelType type)
{
    if (capacity <= 0) {
        return getDefaultCapacity(type);
    }
    
    // 设置合理的容量范围
    int minCapacity = (type == CircularModel) ? 1000 : 10000;
    int maxCapacity = (type == CircularModel) ? 100000 : 10000000;
    
    if (capacity < minCapacity) {
        qWarning() << "LogModelFactory: 容量" << capacity << "过小，调整为最小值" << minCapacity;
        return minCapacity;
    }
    
    if (capacity > maxCapacity) {
        qWarning() << "LogModelFactory: 容量" << capacity << "过大，调整为最大值" << maxCapacity;
        return maxCapacity;
    }
    
    return capacity;
}

std::unique_ptr<BaseLogModel> LogModelFactory::createCircularModel(int capacity)
{
    try {
        auto model = std::make_unique<CircularLogModel>(capacity);
        qDebug() << "LogModelFactory: 成功创建CircularLogModel，容量:" << capacity;
        return std::move(model);
    }
    catch (const std::exception& e) {
        qCritical() << "LogModelFactory: 创建CircularLogModel失败:" << e.what();
        return nullptr;
    }
}

std::unique_ptr<BaseLogModel> LogModelFactory::createFileModel(int capacity)
{
    try {
        auto model = std::make_unique<FileLogModel>(capacity);
        qDebug() << "LogModelFactory: 成功创建FileLogModel，容量:" << capacity;
        return std::move(model);
    }
    catch (const std::exception& e) {
        qCritical() << "LogModelFactory: 创建FileLogModel失败:" << e.what();
        return nullptr;
    }
}
