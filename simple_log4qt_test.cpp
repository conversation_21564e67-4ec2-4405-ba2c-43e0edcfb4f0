#include <QDebug>
#include "streamlined_log4qt_source.h"
#include "logger.h"

int main()
{
    qDebug() << "=== 简单Log4QT捕获测试 ===";
    
    // 1. 初始化Log4QT
    qDebug() << "1. 初始化Log4QT...";
    Logger::init("./logs");
    qDebug() << "✓ Log4QT初始化完成";
    
    // 2. 创建StreamlinedLog4QtSource
    qDebug() << "2. 创建StreamlinedLog4QtSource...";
    StreamlinedLog4QtSource source;
    source.setLoggerName("root");
    qDebug() << "✓ 创建完成，Logger名称:" << source.getLoggerName();
    
    // 3. 连接到Log4QT
    qDebug() << "3. 连接到Log4QT...";
    bool connected = source.connectToSource();
    qDebug() << "连接结果:" << (connected ? "✓ 成功" : "✗ 失败");
    
    if (connected) {
        qDebug() << "✓ 连接状态:" << source.isConnected();
        qDebug() << "✓ 数据源信息:" << source.getSourceInfo();
        
        // 4. 测试日志输出
        qDebug() << "4. 输出测试日志...";
        DEBUG("测试DEBUG日志");
        INFO("测试INFO日志");
        WARN("测试WARN日志");
        ERROR("测试ERROR日志");
        qDebug() << "✓ 日志输出完成";
        
        // 5. 断开连接
        source.disconnect();
        qDebug() << "✓ 已断开连接";
    }
    
    qDebug() << "=== 测试完成 ===";
    return connected ? 0 : 1;
}
