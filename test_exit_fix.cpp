#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTextEdit>
#include <QTimer>
#include <QDebug>
#include <QCloseEvent>

#include "simplelog4qtdatasource.h"
#include "simplelogviewerappender.h"
#include "logentry.h"

/**
 * @brief 测试程序退出时Log4Qt清理的修复效果
 */
class ExitTestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ExitTestWidget(QWidget* parent = nullptr)
        : QWidget(parent)
        , m_dataSource(nullptr)
        , m_logOutput(nullptr)
        , m_statusLabel(nullptr)
    {
        setupUI();
        setWindowTitle("Log4Qt退出清理测试");
        resize(600, 400);
        
        // 注册元类型
        qRegisterMetaType<LogEntry>("LogEntry");
        qRegisterMetaType<QVector<LogEntry>>("QVector<LogEntry>");
    }

protected:
    void closeEvent(QCloseEvent* event) override
    {
        m_logOutput->append("=== 开始程序退出流程 ===");
        m_statusLabel->setText("正在退出...");
        
        // 强制处理事件，确保UI更新
        QApplication::processEvents();
        
        if (m_dataSource) {
            m_logOutput->append("正在清理Log4Qt数据源...");
            QApplication::processEvents();
            
            // 记录开始时间
            QElapsedTimer timer;
            timer.start();
            
            try {
                // 断开连接，这会触发cleanupLog4Qt()
                m_dataSource->disconnect();
                
                qint64 elapsed = timer.elapsed();
                m_logOutput->append(QString("Log4Qt清理完成，耗时: %1ms").arg(elapsed));
                
                if (elapsed > 2000) {
                    m_logOutput->append("警告：清理时间过长，可能存在卡死风险");
                } else {
                    m_logOutput->append("清理时间正常");
                }
                
            } catch (const std::exception& e) {
                m_logOutput->append(QString("清理过程中发生异常: %1").arg(e.what()));
            } catch (...) {
                m_logOutput->append("清理过程中发生未知异常");
            }
            
            QApplication::processEvents();
        }
        
        m_logOutput->append("=== 程序退出流程完成 ===");
        m_statusLabel->setText("退出完成");
        QApplication::processEvents();
        
        // 接受关闭事件
        event->accept();
    }

private slots:
    void createLog4QtSource()
    {
        if (m_dataSource) {
            m_logOutput->append("Log4Qt数据源已存在，先清理...");
            delete m_dataSource;
            m_dataSource = nullptr;
        }
        
        m_logOutput->append("创建Log4Qt数据源...");
        m_dataSource = new SimpleLog4QtDataSource(this);
        
        // 连接信号
        connect(m_dataSource, &SimpleLog4QtDataSource::dataReady,
                this, &ExitTestWidget::onDataReceived);
        connect(m_dataSource, &SimpleLog4QtDataSource::error,
                this, &ExitTestWidget::onError);
        
        m_logOutput->append("尝试连接Log4Qt...");
        if (m_dataSource->connectToSource()) {
            m_logOutput->append("✓ Log4Qt连接成功");
            m_statusLabel->setText("已连接到Log4Qt");
        } else {
            m_logOutput->append("✗ Log4Qt连接失败");
            m_statusLabel->setText("连接失败");
        }
    }
    
    void generateTestLogs()
    {
        if (!m_dataSource || !m_dataSource->isConnected()) {
            m_logOutput->append("请先创建并连接Log4Qt数据源");
            return;
        }
        
        m_logOutput->append("生成测试日志...");
        
        // 生成一些测试日志
        for (int i = 0; i < 10; ++i) {
            LogEntry entry(
                QDateTime::currentDateTime(),
                static_cast<LogEntry::LogLevel>(i % 5),
                QString("TestSource%1").arg(i),
                QString("测试日志消息 %1").arg(i),
                QString("详细信息 %1").arg(i)
            );
            
            // 模拟发送日志到数据源
            QTimer::singleShot(i * 100, [this, entry]() {
                // 这里可以添加实际的日志生成逻辑
                m_logOutput->append(QString("生成日志: %1").arg(entry.message()));
            });
        }
        
        m_statusLabel->setText("正在生成测试日志...");
    }
    
    void testCleanup()
    {
        if (!m_dataSource) {
            m_logOutput->append("没有数据源需要清理");
            return;
        }
        
        m_logOutput->append("=== 开始测试清理过程 ===");
        m_statusLabel->setText("测试清理中...");
        
        QElapsedTimer timer;
        timer.start();
        
        try {
            m_dataSource->disconnect();
            
            qint64 elapsed = timer.elapsed();
            m_logOutput->append(QString("清理完成，耗时: %1ms").arg(elapsed));
            
            if (elapsed > 1000) {
                m_logOutput->append("警告：清理时间较长");
            } else {
                m_logOutput->append("清理时间正常");
            }
            
            m_statusLabel->setText("清理测试完成");
            
        } catch (const std::exception& e) {
            m_logOutput->append(QString("清理测试中发生异常: %1").arg(e.what()));
            m_statusLabel->setText("清理测试异常");
        }
        
        m_logOutput->append("=== 清理测试完成 ===");
    }
    
    void onDataReceived(const QVector<LogEntry>& entries)
    {
        m_logOutput->append(QString("接收到 %1 条日志数据").arg(entries.size()));
    }
    
    void onError(const QString& error)
    {
        m_logOutput->append(QString("错误: %1").arg(error));
    }

private:
    void setupUI()
    {
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // 按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* createBtn = new QPushButton("创建Log4Qt数据源");
        QPushButton* generateBtn = new QPushButton("生成测试日志");
        QPushButton* cleanupBtn = new QPushButton("测试清理");
        QPushButton* exitBtn = new QPushButton("退出程序");
        
        buttonLayout->addWidget(createBtn);
        buttonLayout->addWidget(generateBtn);
        buttonLayout->addWidget(cleanupBtn);
        buttonLayout->addWidget(exitBtn);
        
        connect(createBtn, &QPushButton::clicked, this, &ExitTestWidget::createLog4QtSource);
        connect(generateBtn, &QPushButton::clicked, this, &ExitTestWidget::generateTestLogs);
        connect(cleanupBtn, &QPushButton::clicked, this, &ExitTestWidget::testCleanup);
        connect(exitBtn, &QPushButton::clicked, this, &QWidget::close);
        
        // 日志输出区域
        m_logOutput = new QTextEdit();
        m_logOutput->setReadOnly(true);
        m_logOutput->append("Log4Qt退出清理测试程序");
        m_logOutput->append("1. 点击'创建Log4Qt数据源'建立连接");
        m_logOutput->append("2. 点击'生成测试日志'产生一些日志");
        m_logOutput->append("3. 点击'测试清理'测试清理过程");
        m_logOutput->append("4. 点击'退出程序'测试程序退出时的清理");
        m_logOutput->append("观察清理过程是否会卡死或超时");
        m_logOutput->append("========================================");
        
        // 状态标签
        m_statusLabel = new QLabel("准备就绪");
        
        layout->addLayout(buttonLayout);
        layout->addWidget(m_logOutput, 1);
        layout->addWidget(m_statusLabel);
    }

private:
    SimpleLog4QtDataSource* m_dataSource;
    QTextEdit* m_logOutput;
    QLabel* m_statusLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ExitTestWidget window;
    window.show();
    
    return app.exec();
}

#include "test_exit_fix.moc"
