QT += core gui widgets

CONFIG += c++17

TARGET = TestViewFix
TEMPLATE = app

# 定义宏
DEFINES += SIMPLE_VERSION
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

HEADERS += \
    logviewer_global.h \
    logentry.h \
    baselogmodel.h \
    filelogmodel.h \
    logsortfilterproxymodel.h \
    filterexpression.h \
    compositefilterexpression.h

SOURCES += \
    test_view_fix.cpp \
    logentry.cpp \
    baselogmodel.cpp \
    filelogmodel.cpp \
    logsortfilterproxymodel.cpp \
    filterexpression.cpp \
    compositefilterexpression.cpp
