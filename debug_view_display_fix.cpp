#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTableView>
#include <QLabel>
#include <QFileDialog>
#include <QDebug>
#include <QTimer>
#include <QMessageBox>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "simplefiledatasource.h"
#include "logentry.h"

/**
 * @brief 调试视图显示问题的测试程序
 * 
 * 这个程序专门用于诊断为什么FileLogModel有数据但视图不显示的问题
 */
class DebugViewWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DebugViewWidget(QWidget* parent = nullptr)
        : QWidget(parent)
        , m_model(nullptr)
        , m_proxyModel(nullptr)
        , m_dataSource(nullptr)
        , m_tableView(nullptr)
        , m_statusLabel(nullptr)
    {
        setupUI();
        setupModel();
        connectSignals();
        
        setWindowTitle("调试视图显示问题");
        resize(1000, 600);
    }

private slots:
    void loadTestFile()
    {
        QString fileName = QFileDialog::getOpenFileName(
            this,
            "选择日志文件",
            "",
            "日志文件 (*.log *.txt);;所有文件 (*.*)"
        );
        
        if (!fileName.isEmpty()) {
            loadFile(fileName);
        }
    }
    
    void loadFile(const QString& filePath)
    {
        m_statusLabel->setText("开始加载文件...");
        
        // 清除现有数据
        if (m_model) {
            m_model->clear();
        }
        
        // 设置数据源
        m_dataSource->setFilePath(filePath);
        m_dataSource->setEncoding("UTF-8");
        
        // 连接并加载
        if (m_dataSource->connectToSource()) {
            m_dataSource->loadData();
            m_statusLabel->setText("文件加载请求已发送，等待数据...");
        } else {
            m_statusLabel->setText("连接文件失败");
        }
    }
    
    void onDataReceived(const QVector<LogEntry>& entries)
    {
        qDebug() << "=== onDataReceived 调试信息 ===";
        qDebug() << "接收到数据条目数:" << entries.size();
        
        if (entries.isEmpty()) {
            qDebug() << "警告：接收到空数据！";
            m_statusLabel->setText("接收到空数据");
            return;
        }
        
        // 显示前几条数据的内容
        for (int i = 0; i < qMin(3, entries.size()); ++i) {
            const LogEntry& entry = entries[i];
            qDebug() << QString("条目 %1: [%2] %3 - %4")
                        .arg(i)
                        .arg(entry.levelString())
                        .arg(entry.timestamp().toString("hh:mm:ss"))
                        .arg(entry.message().left(50));
        }
        
        // 添加到模型
        qDebug() << "添加数据到模型前 - 模型行数:" << m_model->rowCount();
        m_model->addLogEntries(entries);
        qDebug() << "添加数据到模型后 - 模型行数:" << m_model->rowCount();
        
        // 检查代理模型
        qDebug() << "代理模型行数:" << m_proxyModel->rowCount();
        
        // 强制更新视图
        m_tableView->reset();
        m_tableView->update();
        
        // 更新状态
        m_statusLabel->setText(QString("数据加载完成: %1 条，模型行数: %2，代理行数: %3")
                              .arg(entries.size())
                              .arg(m_model->rowCount())
                              .arg(m_proxyModel->rowCount()));
        
        qDebug() << "=== onDataReceived 调试信息结束 ===";
    }
    
    void onDataSourceError(const QString& error)
    {
        qDebug() << "数据源错误:" << error;
        m_statusLabel->setText("错误: " + error);
    }
    
    void checkModelData()
    {
        qDebug() << "=== 手动检查模型数据 ===";
        qDebug() << "FileLogModel 行数:" << m_model->rowCount();
        qDebug() << "ProxyModel 行数:" << m_proxyModel->rowCount();
        qDebug() << "TableView 行数:" << m_tableView->model()->rowCount();
        
        // 检查模型中的实际数据
        for (int i = 0; i < qMin(5, m_model->rowCount()); ++i) {
            LogEntry entry = m_model->getLogEntry(i);
            qDebug() << QString("模型行 %1: %2").arg(i).arg(entry.message().left(50));
        }
        
        // 检查代理模型的过滤设置
        qDebug() << "代理模型过滤模式:" << m_proxyModel->filterPattern();
        qDebug() << "代理模型级别过滤器包含Info:" << m_proxyModel->isLevelVisible(LogEntry::LogLevel::Info);
        
        m_statusLabel->setText(QString("检查完成 - 模型:%1行，代理:%2行，视图:%3行")
                              .arg(m_model->rowCount())
                              .arg(m_proxyModel->rowCount())
                              .arg(m_tableView->model()->rowCount()));
    }
    
    void forceRefresh()
    {
        qDebug() << "强制刷新视图";
        m_proxyModel->invalidate();
        m_tableView->reset();
        m_tableView->update();
        m_tableView->repaint();
        
        QTimer::singleShot(100, [this]() {
            m_tableView->scrollToBottom();
        });
    }
    
    void addTestData()
    {
        qDebug() << "添加测试数据";
        
        QVector<LogEntry> testEntries;
        for (int i = 0; i < 5; ++i) {
            LogEntry entry(
                QDateTime::currentDateTime(),
                LogEntry::LogLevel::Info,
                QString("TestSource:%1").arg(i),
                QString("测试消息 %1").arg(i),
                QString("测试详情 %1").arg(i)
            );
            testEntries.append(entry);
        }
        
        onDataReceived(testEntries);
    }

private:
    void setupUI()
    {
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        
        // 按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* loadBtn = new QPushButton("加载文件");
        QPushButton* checkBtn = new QPushButton("检查模型数据");
        QPushButton* refreshBtn = new QPushButton("强制刷新");
        QPushButton* testBtn = new QPushButton("添加测试数据");
        
        buttonLayout->addWidget(loadBtn);
        buttonLayout->addWidget(checkBtn);
        buttonLayout->addWidget(refreshBtn);
        buttonLayout->addWidget(testBtn);
        buttonLayout->addStretch();
        
        connect(loadBtn, &QPushButton::clicked, this, &DebugViewWidget::loadTestFile);
        connect(checkBtn, &QPushButton::clicked, this, &DebugViewWidget::checkModelData);
        connect(refreshBtn, &QPushButton::clicked, this, &DebugViewWidget::forceRefresh);
        connect(testBtn, &QPushButton::clicked, this, &DebugViewWidget::addTestData);
        
        // 表格视图
        m_tableView = new QTableView();
        m_tableView->setAlternatingRowColors(true);
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_tableView->setSortingEnabled(true);
        
        // 状态标签
        m_statusLabel = new QLabel("准备就绪");
        
        mainLayout->addLayout(buttonLayout);
        mainLayout->addWidget(m_tableView, 1);
        mainLayout->addWidget(m_statusLabel);
    }
    
    void setupModel()
    {
        // 创建FileLogModel
        m_model = new FileLogModel(100000, this);
        
        // 创建代理模型
        m_proxyModel = new LogSortFilterProxyModel(this);
        m_proxyModel->setSourceModel(m_model);
        
        // 设置到视图
        m_tableView->setModel(m_proxyModel);
        
        // 创建数据源
        m_dataSource = new SimpleFileDataSource(this);
        
        qDebug() << "模型和数据源创建完成";
    }
    
    void connectSignals()
    {
        // 连接数据源信号
        connect(m_dataSource, &SimpleFileDataSource::dataReady,
                this, &DebugViewWidget::onDataReceived);
        connect(m_dataSource, &SimpleFileDataSource::error,
                this, &DebugViewWidget::onDataSourceError);
        
        // 连接模型信号
        connect(m_model, &FileLogModel::modelDataChanged, [this]() {
            qDebug() << "模型数据变化信号触发";
        });
        
        qDebug() << "信号连接完成";
    }

private:
    FileLogModel* m_model;
    LogSortFilterProxyModel* m_proxyModel;
    SimpleFileDataSource* m_dataSource;
    QTableView* m_tableView;
    QLabel* m_statusLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 注册元类型
    qRegisterMetaType<LogEntry>("LogEntry");
    qRegisterMetaType<QVector<LogEntry>>("QVector<LogEntry>");
    qRegisterMetaType<LogEntry::LogLevel>("LogEntry::LogLevel");
    
    DebugViewWidget window;
    window.show();
    
    return app.exec();
}

#include "debug_view_display_fix.moc"
