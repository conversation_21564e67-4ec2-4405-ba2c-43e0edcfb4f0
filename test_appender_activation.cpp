#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "logger.h"
#include "streamlined_log4qt_source.h"

/**
 * @brief 测试StreamlinedLogAppender的activateOptions修复
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "\n=== 测试StreamlinedLogAppender的activateOptions修复 ===";
    
    // 1. 初始化Log4QT
    qDebug() << "1. 初始化Log4QT...";
    Logger::init("./logs");
    qDebug() << "✓ Log4QT初始化完成";
    
    // 2. 创建StreamlinedLog4QtSource
    qDebug() << "2. 创建StreamlinedLog4QtSource...";
    StreamlinedLog4QtSource source;
    
    // 3. 设置信号监听
    int receivedCount = 0;
    QObject::connect(&source, &IDataSource::dataReady, 
                     [&receivedCount](const QVector<LogEntry>& entries) {
        receivedCount += entries.size();
        qDebug() << QString("✓ 接收到 %1 条日志，总计: %2").arg(entries.size()).arg(receivedCount);
        for (const LogEntry& entry : entries) {
            qDebug() << QString("  - [%1] %2: %3")
                        .arg(entry.levelString())
                        .arg(entry.source())
                        .arg(entry.message());
        }
    });
    
    QObject::connect(&source, &IDataSource::error,
                     [](const QString& error) {
        qDebug() << "✗ 错误:" << error;
    });
    
    // 4. 连接到Log4QT（这里会调用activateOptions）
    qDebug() << "3. 连接到Log4QT（会调用activateOptions）...";
    source.setLoggerName("root");
    bool connected = source.connectToSource();
    qDebug() << "连接结果:" << (connected ? "✓ 成功" : "✗ 失败");
    
    if (!connected) {
        qDebug() << "连接失败，退出测试";
        return 1;
    }
    
    // 5. 等待一下，确保连接完成
    QTimer::singleShot(500, [&]() {
        qDebug() << "\n4. 开始输出测试日志...";
        
        // 输出不同级别的日志
        qDebug() << "输出DEBUG日志...";
        DEBUG("测试DEBUG级别 - activateOptions修复后");
        
        qDebug() << "输出INFO日志...";
        INFO("测试INFO级别 - activateOptions修复后");
        
        qDebug() << "输出WARN日志...";
        WARN("测试WARN级别 - activateOptions修复后");
        
        qDebug() << "输出ERROR日志...";
        ERROR("测试ERROR级别 - activateOptions修复后");
        
        qDebug() << "输出FATAL日志...";
        FATAL("测试FATAL级别 - activateOptions修复后");
        
        qDebug() << "--- 日志输出完成，等待接收... ---\n";
    });
    
    // 6. 检查结果
    QTimer::singleShot(3000, [&]() {
        qDebug() << "\n=== 最终测试结果 ===";
        qDebug() << QString("总共接收到的日志条数: %1").arg(receivedCount);
        
        if (receivedCount >= 5) {
            qDebug() << "🎉 测试成功！";
            qDebug() << "✓ activateOptions()方法修复有效";
            qDebug() << "✓ StreamlinedLogAppender正确激活";
            qDebug() << "✓ 能够捕获Log4QT输出";
            qDebug() << "✓ 日志级别映射正常";
        } else {
            qDebug() << "❌ 测试失败！";
            qDebug() << "问题分析：";
            if (receivedCount == 0) {
                qDebug() << "- append()方法可能仍未被调用";
                qDebug() << "- 可能存在其他Log4Qt配置问题";
            } else {
                qDebug() << "- 部分日志被接收，可能存在级别过滤问题";
            }
        }
        
        app.quit();
    });
    
    return app.exec();
}
