#ifndef FILELOGMODEL_H
#define FILELOGMODEL_H

#include "baselogmodel.h"
#include <QVector>

/**
 * @brief 文件日志Model
 * 
 * 专门用于文件日志查看，保留所有数据
 * 特点：
 * - 完整数据保留
 * - 支持大文件处理
 * - 可选的分页加载
 * - 内存使用监控
 */
class LOGVIEWER_EXPORT FileLogModel : public BaseLogModel
{
    Q_OBJECT

public:
    explicit FileLogModel(int maxCapacity = 1000000, QObject* parent = nullptr);
    ~FileLogModel() override;

    // ========== QAbstractTableModel实现 ==========
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;

    // ========== BaseLogModel实现 ==========
    void addLogEntry(const LogEntry& entry) override;
    void addLogEntries(const QVector<LogEntry>& entries) override;
    LogEntry getLogEntry(int row) const override;
    QVector<LogEntry> getEntries(int startIndex, int count) const override;
    void clear() override;
    qint64 getMemoryUsage() const override;
    int getTotalCount() const override;
    QString getModelType() const override { return "FileLogModel"; }

    // ========== 文件Model特有功能 ==========
    
    /**
     * @brief 获取最大容量
     */
    int getMaxCapacity() const;
    
    /**
     * @brief 设置最大容量
     */
    void setMaxCapacity(int maxCapacity);
    
    /**
     * @brief 设置最大条目数（兼容性方法）
     */
    void setMaxEntries(uint64_t maxEntries);
    
    /**
     * @brief 启用/禁用分页
     * @param enable 是否启用分页
     * @param pageSize 页面大小
     */
    void enablePaging(bool enable, int pageSize = 10000);
    
    /**
     * @brief 是否启用了分页
     */
    bool isPagingEnabled() const;
    
    /**
     * @brief 获取页面大小
     */
    int getPageSize() const;
    
    /**
     * @brief 获取总页数
     */
    int getPageCount() const;
    
    /**
     * @brief 获取当前页索引
     */
    int getCurrentPage() const;
    
    /**
     * @brief 加载指定页面
     * @param pageIndex 页面索引
     */
    void loadPage(int pageIndex);
    
    /**
     * @brief 预加载数据（用于大文件）
     * @param entries 预加载的数据
     */
    void preloadData(const QVector<LogEntry>& entries);
    
    /**
     * @brief 获取数据使用率
     */
    double getUsageRatio() const;
    
    /**
     * @brief 删除指定范围的条目
     * @param startIndex 起始索引
     * @param count 删除数量
     */
    void removeEntriesAt(int startIndex, int count);
    
    /**
     * @brief 保留指定范围的条目，删除其他条目
     * @param startIndex 起始索引
     * @param endIndex 结束索引
     * @param marginCount 边距数量
     */
    void retainDataRange(int startIndex, int endIndex, int marginCount);

signals:
    /**
     * @brief 页面变化信号
     * @param currentPage 当前页
     * @param totalPages 总页数
     */
    void pageChanged(int currentPage, int totalPages);

private:
    QVector<LogEntry> m_entries;        ///< 当前显示的数据
    QVector<LogEntry> m_allEntries;     ///< 所有数据（分页模式下）
    int m_maxCapacity;                  ///< 最大容量
    
    // 分页相关
    bool m_enablePaging;                ///< 是否启用分页
    int m_pageSize;                     ///< 页面大小
    int m_currentPage;                  ///< 当前页索引
    
    /**
     * @brief 检查是否需要内存清理
     */
    bool needsMemoryCleanup() const;
    
    /**
     * @brief 执行内存清理
     */
    void performMemoryCleanup();
    
    /**
     * @brief 更新分页数据
     */
    void updatePagedData();
    
    /**
     * @brief 格式化日志条目数据（适配LogEntry接口）
     */
    QVariant formatFileLogEntryData(const LogEntry& entry, int column, int role) const;
    
    /**
     * @brief 安全地添加条目（检查容量限制）
     */
    void safeAddEntry(const LogEntry& entry);
    
    /**
     * @brief 安全地添加多个条目（检查容量限制）
     */
    void safeAddEntries(const QVector<LogEntry>& entries);
};

#endif // FILELOGMODEL_H
