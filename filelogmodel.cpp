#include "filelogmodel.h"
#include <QColor>
#include <QBrush>
#include <QFont>
#include <QDebug>
#include <algorithm>

FileLogModel::FileLogModel(int maxCapacity, QObject* parent)
    : BaseLogModel(parent)
    , m_maxCapacity(maxCapacity > 0 ? maxCapacity : 1000000)
    , m_enablePaging(false)
    , m_pageSize(10000)
    , m_currentPage(0)
{
    // 预分配内存以提高性能
    m_entries.reserve(qMin(m_maxCapacity, 50000));
    
    qDebug() << "FileLogModel initialized with max capacity:" << m_maxCapacity;
}

FileLogModel::~FileLogModel()
{
    qDebug() << "FileLogModel destroyed";
}

int FileLogModel::rowCount(const QModelIndex& parent) const
{
    if (parent.isValid())
        return 0;
    
    QMutexLocker locker(&m_mutex);
    return m_entries.size();
}

QVariant FileLogModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid())
        return QVariant();

    LogEntry entry;
    int actualColumn = -1;

    {
        QMutexLocker locker(&m_mutex);
        if (index.row() >= m_entries.size() || index.row() < 0)
            return QVariant();

        entry = m_entries.at(index.row());
        actualColumn = mapToActualColumn(index.column());
    }

    if (actualColumn < 0 || actualColumn >= static_cast<int>(ColumnCount))
        return QVariant();

    return formatFileLogEntryData(entry, actualColumn, role);
}

void FileLogModel::addLogEntry(const LogEntry& entry)
{
    try {
        QMutexLocker locker(&m_mutex);
        int currentSize = m_entries.size();
        locker.unlock();

        // 检查是否需要内存清理
        if (needsMemoryCleanup()) {
            performMemoryCleanup();
        }

        beginInsertRows(QModelIndex(), currentSize, currentSize);
        locker.relock();
        safeAddEntry(entry);
        int newRowIndex = m_entries.size() - 1;
        locker.unlock();
        endInsertRows();

        // 发出标准的dataChanged信号通知View更新
        QModelIndex topLeft = index(newRowIndex, 0);
        QModelIndex bottomRight = index(newRowIndex, ColumnCount - 1);
        emit dataChanged(topLeft, bottomRight);

        // 发出自定义的modelDataChanged信号用于状态更新
        emit modelDataChanged();
    }
    catch (const std::exception& e) {
        qWarning() << "FileLogModel: 添加日志条目时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "FileLogModel: 添加日志条目时发生未知异常";
    }
}

void FileLogModel::addLogEntries(const QVector<LogEntry>& entries)
{
    if (entries.isEmpty()) {
        return;
    }

    try {
        // 检查是否需要内存清理
        if (needsMemoryCleanup()) {
            performMemoryCleanup();
        }

        QMutexLocker locker(&m_mutex);
        int currentSize = m_entries.size();
        int newSize = currentSize + entries.size();
        locker.unlock();

        beginInsertRows(QModelIndex(), currentSize, newSize - 1);
        locker.relock();
        safeAddEntries(entries);
        int endRowIndex = m_entries.size() - 1;
        locker.unlock();
        endInsertRows();

        // 发出标准的dataChanged信号通知View更新
        if (endRowIndex >= 0) {
            QModelIndex topLeft = index(currentSize, 0);
            QModelIndex bottomRight = index(endRowIndex, ColumnCount - 1);
            emit dataChanged(topLeft, bottomRight);
        }

        // 发出自定义的modelDataChanged信号用于状态更新
        emit modelDataChanged();
        
        qDebug() << "FileLogModel: 添加了" << entries.size() << "条数据，总数:" << getTotalCount();
    }
    catch (const std::exception& e) {
        qWarning() << "FileLogModel: 处理日志条目时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "FileLogModel: 处理日志条目时发生未知异常";
    }
}

LogEntry FileLogModel::getLogEntry(int row) const
{
    QMutexLocker locker(&m_mutex);
    return (row >= 0 && row < m_entries.size()) ? m_entries.at(row) : LogEntry();
}

QVector<LogEntry> FileLogModel::getEntries(int startIndex, int count) const
{
    QMutexLocker locker(&m_mutex);
    
    if (startIndex < 0 || startIndex >= m_entries.size()) {
        return QVector<LogEntry>();
    }
    
    int endIndex = qMin(startIndex + count, m_entries.size());
    QVector<LogEntry> result;
    result.reserve(endIndex - startIndex);
    
    for (int i = startIndex; i < endIndex; ++i) {
        result.append(m_entries.at(i));
    }
    
    return result;
}

void FileLogModel::clear()
{
    try {
        qDebug() << "FileLogModel::clear() 开始执行";

        beginResetModel();
        {
            QMutexLocker locker(&m_mutex);
            int oldSize = m_entries.size();
            m_entries.clear();
            m_allEntries.clear();
            m_currentPage = 0;
            qDebug() << "FileLogModel: 清除了" << oldSize << "条数据";
        }
        endResetModel();

        // 内存优化
        {
            QMutexLocker locker(&m_mutex);
            m_entries.squeeze();
            m_allEntries.squeeze();
        }
        
        // 发出标准的dataChanged信号通知View更新
        emit QAbstractItemModel::dataChanged(QModelIndex(), QModelIndex());

        // 发出自定义的dataChanged信号
        emit BaseLogModel::modelDataChanged();
        if (m_enablePaging) {
            emit pageChanged(0, 0);
        }
        
        qDebug() << "FileLogModel::clear() 执行完成";
    }
    catch (const std::exception& e) {
        qWarning() << "FileLogModel: 清除日志时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "FileLogModel: 清除日志时发生未知异常";
    }
}

qint64 FileLogModel::getMemoryUsage() const
{
    QMutexLocker locker(&m_mutex);
    
    // 估算内存使用量
    qint64 entriesMemory = m_entries.size() * sizeof(LogEntry);
    qint64 allEntriesMemory = m_allEntries.size() * sizeof(LogEntry);
    
    // 估算字符串内容的内存使用
    qint64 stringMemory = 0;
    for (const LogEntry& entry : m_entries) {
        stringMemory += entry.message().size() * sizeof(QChar);
        stringMemory += entry.source().size() * sizeof(QChar);
        stringMemory += entry.details().size() * sizeof(QChar);
        stringMemory += entry.levelString().size() * sizeof(QChar);
    }
    
    return entriesMemory + allEntriesMemory + stringMemory;
}

int FileLogModel::getTotalCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_enablePaging ? m_allEntries.size() : m_entries.size();
}

int FileLogModel::getMaxCapacity() const
{
    QMutexLocker locker(&m_mutex);
    return m_maxCapacity;
}

void FileLogModel::setMaxCapacity(int maxCapacity)
{
    QMutexLocker locker(&m_mutex);
    m_maxCapacity = maxCapacity;
    
    // 如果当前数据超过新的容量限制，进行清理
    if (m_entries.size() > maxCapacity) {
        performMemoryCleanup();
    }
}

void FileLogModel::setMaxEntries(uint64_t maxEntries)
{
    setMaxCapacity(static_cast<int>(maxEntries));
}

void FileLogModel::enablePaging(bool enable, int pageSize)
{
    QMutexLocker locker(&m_mutex);
    
    bool wasEnabled = m_enablePaging;
    m_enablePaging = enable;
    m_pageSize = pageSize > 0 ? pageSize : 10000;
    
    if (enable && !wasEnabled) {
        // 启用分页：将当前数据移到allEntries
        m_allEntries = m_entries;
        m_currentPage = 0;
        locker.unlock();
        updatePagedData();
    } else if (!enable && wasEnabled) {
        // 禁用分页：将所有数据移回entries
        m_entries = m_allEntries;
        m_allEntries.clear();
        m_currentPage = 0;
        locker.unlock();
        
        beginResetModel();
        endResetModel();
    }
    
    if (enable) {
        emit pageChanged(m_currentPage, getPageCount());
    }
}

bool FileLogModel::isPagingEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_enablePaging;
}

int FileLogModel::getPageSize() const
{
    QMutexLocker locker(&m_mutex);
    return m_pageSize;
}

int FileLogModel::getPageCount() const
{
    QMutexLocker locker(&m_mutex);
    if (!m_enablePaging || m_pageSize <= 0) {
        return 1;
    }
    return (m_allEntries.size() + m_pageSize - 1) / m_pageSize;
}

int FileLogModel::getCurrentPage() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentPage;
}

void FileLogModel::loadPage(int pageIndex)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_enablePaging) {
        qWarning() << "FileLogModel: 分页未启用";
        return;
    }
    
    int totalPages = getPageCount();
    if (pageIndex < 0 || pageIndex >= totalPages) {
        qWarning() << "FileLogModel: 无效的页面索引:" << pageIndex;
        return;
    }
    
    m_currentPage = pageIndex;
    locker.unlock();
    
    updatePagedData();
    emit pageChanged(m_currentPage, totalPages);
}

void FileLogModel::preloadData(const QVector<LogEntry>& entries)
{
    if (entries.isEmpty()) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    if (m_enablePaging) {
        m_allEntries = entries;
        m_currentPage = 0;
        locker.unlock();
        updatePagedData();
        emit pageChanged(0, getPageCount());
    } else {
        m_entries = entries;
        locker.unlock();
        beginResetModel();
        endResetModel();
    }
    
    // 发出标准的dataChanged信号通知View更新（全部数据变化）
    if (rowCount() > 0) {
        QModelIndex topLeft = index(0, 0);
        QModelIndex bottomRight = index(rowCount() - 1, columnCount() - 1);
        emit dataChanged(topLeft, bottomRight);
    }

    // 发出自定义的modelDataChanged信号用于状态更新
    emit modelDataChanged();
    qDebug() << "FileLogModel: 预加载了" << entries.size() << "条数据";
}

double FileLogModel::getUsageRatio() const
{
    QMutexLocker locker(&m_mutex);
    if (m_maxCapacity == 0) return 0.0;

    int currentSize = m_enablePaging ? m_allEntries.size() : m_entries.size();
    return static_cast<double>(currentSize) / m_maxCapacity;
}

void FileLogModel::removeEntriesAt(int startIndex, int count)
{
    if (count <= 0) return;

    QMutexLocker locker(&m_mutex);

    QVector<LogEntry>& targetEntries = m_enablePaging ? m_allEntries : m_entries;

    if (startIndex < 0 || startIndex >= targetEntries.size()) {
        qWarning() << "FileLogModel: 无效的删除起始索引:" << startIndex;
        return;
    }

    int actualCount = qMin(count, targetEntries.size() - startIndex);
    int endIndex = startIndex + actualCount - 1;

    locker.unlock();

    beginRemoveRows(QModelIndex(), startIndex, endIndex);
    locker.relock();
    targetEntries.remove(startIndex, actualCount);
    locker.unlock();
    endRemoveRows();

    if (m_enablePaging) {
        updatePagedData();
        emit pageChanged(m_currentPage, getPageCount());
    }

    // 发出标准的dataChanged信号通知View更新
    emit QAbstractItemModel::dataChanged(QModelIndex(), QModelIndex());

    // 发出自定义的dataChanged信号
    emit BaseLogModel::modelDataChanged();
    qDebug() << "FileLogModel: 删除了" << actualCount << "条数据，从索引" << startIndex << "开始";
}

void FileLogModel::retainDataRange(int startIndex, int endIndex, int marginCount)
{
    QMutexLocker locker(&m_mutex);

    QVector<LogEntry>& targetEntries = m_enablePaging ? m_allEntries : m_entries;

    if (startIndex < 0 || endIndex >= targetEntries.size() || startIndex > endIndex) {
        qWarning() << "FileLogModel: 无效的保留范围:" << startIndex << "到" << endIndex;
        return;
    }

    // 计算实际保留范围（包含边距）
    int actualStart = qMax(0, startIndex - marginCount);
    int actualEnd = qMin(targetEntries.size() - 1, endIndex + marginCount);

    // 创建新的数据向量
    QVector<LogEntry> newEntries;
    newEntries.reserve(actualEnd - actualStart + 1);

    for (int i = actualStart; i <= actualEnd; ++i) {
        newEntries.append(targetEntries.at(i));
    }

    int oldSize = targetEntries.size();
    targetEntries = newEntries;

    locker.unlock();

    beginResetModel();
    endResetModel();

    if (m_enablePaging) {
        m_currentPage = 0;
        updatePagedData();
        emit pageChanged(0, getPageCount());
    }

    // 发出标准的dataChanged信号通知View更新（全部数据变化）
    if (rowCount() > 0) {
        QModelIndex topLeft = index(0, 0);
        QModelIndex bottomRight = index(rowCount() - 1, columnCount() - 1);
        emit dataChanged(topLeft, bottomRight);
    }

    // 发出自定义的modelDataChanged信号用于状态更新
    emit modelDataChanged();
    qDebug() << "FileLogModel: 保留了范围" << actualStart << "到" << actualEnd
             << "的数据，原有" << oldSize << "条，现有" << newEntries.size() << "条";
}

bool FileLogModel::needsMemoryCleanup() const
{
    QMutexLocker locker(&m_mutex);

    int currentSize = m_enablePaging ? m_allEntries.size() : m_entries.size();

    // 当数据量超过最大容量的90%时需要清理
    return currentSize >= m_maxCapacity * 0.9;
}

void FileLogModel::performMemoryCleanup()
{
    QMutexLocker locker(&m_mutex);

    QVector<LogEntry>& targetEntries = m_enablePaging ? m_allEntries : m_entries;

    if (targetEntries.size() <= m_maxCapacity * 0.8) {
        return; // 不需要清理
    }

    qDebug() << "FileLogModel: 开始内存清理，当前大小:" << targetEntries.size();

    // 保留最新的80%数据
    int targetSize = static_cast<int>(m_maxCapacity * 0.8);
    int removeCount = targetEntries.size() - targetSize;

    if (removeCount > 0) {
        targetEntries.remove(0, removeCount);
        targetEntries.squeeze();

        qDebug() << "FileLogModel: 内存清理完成，删除了" << removeCount << "条旧数据，当前大小:" << targetEntries.size();

        locker.unlock();

        beginResetModel();
        endResetModel();

        if (m_enablePaging) {
            m_currentPage = 0;
            updatePagedData();
            emit pageChanged(0, getPageCount());
        }

        emit memoryWarning();

        // 发出标准的dataChanged信号通知View更新（全部数据变化）
        if (rowCount() > 0) {
            QModelIndex topLeft = index(0, 0);
            QModelIndex bottomRight = index(rowCount() - 1, columnCount() - 1);
            emit dataChanged(topLeft, bottomRight);
        }

        // 发出自定义的modelDataChanged信号用于状态更新
        emit modelDataChanged();
    }
}

void FileLogModel::updatePagedData()
{
    if (!m_enablePaging) {
        return;
    }

    QMutexLocker locker(&m_mutex);

    int startIndex = m_currentPage * m_pageSize;
    int endIndex = qMin(startIndex + m_pageSize, m_allEntries.size());

    m_entries.clear();
    m_entries.reserve(endIndex - startIndex);

    for (int i = startIndex; i < endIndex; ++i) {
        m_entries.append(m_allEntries.at(i));
    }

    locker.unlock();

    beginResetModel();
    endResetModel();

    qDebug() << "FileLogModel: 更新分页数据，页面" << m_currentPage
             << "，显示" << m_entries.size() << "条数据";
}

QVariant FileLogModel::formatFileLogEntryData(const LogEntry& entry, int column, int role) const
{
    switch (role) {
        case Qt::DisplayRole:
            switch (column) {
                case TimestampColumn:
                    return entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz");
                case LevelColumn:
                    return entry.levelString();
                case SourceColumn:
                    return entry.source();
                case MessageColumn:
                    return entry.message();
                case DetailsColumn:
                    return entry.details();
                default:
                    return QVariant();
            }

        case Qt::BackgroundRole:
            switch (entry.level()) {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(248, 248, 248));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(255, 255, 255));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(255, 252, 230));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(255, 235, 235));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(255, 220, 220));
                default:
                    return QVariant();
            }

        case Qt::ForegroundRole:
            switch (entry.level()) {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(120, 120, 120));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(0, 0, 0));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(180, 120, 0));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(220, 20, 20));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(180, 0, 0));
                default:
                    return QVariant();
            }

        case Qt::FontRole: {
            QFont font;
            if (entry.level() == LogEntry::LogLevel::Critical ||
                entry.level() == LogEntry::LogLevel::Error) {
                font.setBold(true);
            }
            return font;
        }

        case Qt::TextAlignmentRole:
            switch (column) {
                case TimestampColumn:
                case LevelColumn:
                    return Qt::AlignCenter;
                default:
                    return QVariant(Qt::AlignLeft | Qt::AlignVCenter);
            }

        case Qt::ToolTipRole:
            return QString("时间: %1\n级别: %2\n来源: %3\n消息: %4\n详情: %5")
                   .arg(entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                   .arg(entry.levelString())
                   .arg(entry.source())
                   .arg(entry.message())
                   .arg(entry.details());

        default:
            return QVariant();
    }
}

void FileLogModel::safeAddEntry(const LogEntry& entry)
{
    // 此方法应在锁内调用
    QVector<LogEntry>& targetEntries = m_enablePaging ? m_allEntries : m_entries;

    if (targetEntries.size() >= m_maxCapacity) {
        // 删除最旧的条目为新条目腾出空间
        targetEntries.removeFirst();
    }

    targetEntries.append(entry);
}

void FileLogModel::safeAddEntries(const QVector<LogEntry>& entries)
{
    // 此方法应在锁内调用
    QVector<LogEntry>& targetEntries = m_enablePaging ? m_allEntries : m_entries;

    for (const LogEntry& entry : entries) {
        if (targetEntries.size() >= m_maxCapacity) {
            targetEntries.removeFirst();
        }
        targetEntries.append(entry);
    }
}
