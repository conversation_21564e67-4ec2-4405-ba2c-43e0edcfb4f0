#include "simplelogviewerappender.h"
#include <QDateTime>
#include <QDebug>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/level.h>
#include <log4qt/logger.h>
#include <log4qt/ndc.h>
#include <log4qt/mdc.h>
#endif

SimpleLogViewerAppender::SimpleLogViewerAppender(QObject* parent)
#ifdef LOG4QT_AVAILABLE
    : Log4Qt::AppenderSkeleton(parent)
#else
    : QObject(parent)
#endif
    , m_enabled(true)
    , m_totalEntryCount(0)
{
    qDebug() << "SimpleLogViewerAppender created";
}

SimpleLogViewerAppender::~SimpleLogViewerAppender()
{
    qDebug() << "SimpleLogViewerAppender destroyed, total entries processed:" << m_totalEntryCount;
}

void SimpleLogViewerAppender::setEnabled(bool enabled)
{
    if (m_enabled != enabled) {
        m_enabled = enabled;
    }
}

#ifdef LOG4QT_AVAILABLE
// ========== Log4Qt Appender 接口实现 ==========

void SimpleLogViewerAppender::append(const Log4Qt::LoggingEvent& event)
{
    if (!m_enabled) {
        return;
    }

    // 转换Log4Qt事件为LogEntry
    LogEntry entry = convertToLogEntry(event);

    // 更新计数器
    m_totalEntryCount++;

    // 发出新日志条目信号
    emit newLogEntry(entry);
}

bool SimpleLogViewerAppender::requiresLayout() const
{
    // 我们不使用布局，因为我们直接处理LoggingEvent对象
    return false;
}

LogEntry SimpleLogViewerAppender::convertToLogEntry(const Log4Qt::LoggingEvent& event) const
{
    QDateTime timestamp = QDateTime::fromMSecsSinceEpoch(event.timeStamp());

        // 获取日志级别 - Log4Qt 1.5.1 返回的是Level对象
        LogEntry::LogLevel level = convertLogLevel(event.level().toInt());

        // 获取logger名称和消息
        QString source;
        if (event.logger())
            source = event.logger()->name();
        else
            source = "Unknown";

        QString message = event.message();

        // 构建详细信息
        QString details;

        // 获取NDC
        QString ndc = event.ndc();
        if (!ndc.isEmpty()) {
            details += tr("NDC: %1\n").arg(ndc);
        }

        // 获取MDC/属性
        QHash<QString, QString> properties = event.properties();
        if (!properties.isEmpty()) {
            details += tr("属性:\n");
            for (auto it = properties.constBegin(); it != properties.constEnd(); ++it) {
                details += tr("  %1: %2\n").arg(it.key(), it.value());
            }
        }

        // 获取位置信息 - 使用MessageContext
        Log4Qt::MessageContext context = event.context();
        if (context.file && context.line > 0) {
            details += tr("位置: %1:%2 (%3)\n")
                     .arg(QString::fromLatin1(context.file))
                     .arg(context.line)
                     .arg(QString::fromLatin1(context.function));
        }

        // 获取线程名称
        details += tr("线程: %1\n").arg(event.threadName());

        // 改用properties中可能存在的相关信息
        if (properties.contains("fileName")) {
            details += tr("文件: %1\n").arg(properties.value("fileName"));
        }

        if (properties.contains("category")) {
            details += tr("类别: %1\n").arg(properties.value("category"));
        }

        return LogEntry(timestamp, level, source, message, details);
}

LogEntry::LogLevel SimpleLogViewerAppender::convertLogLevel(int log4qtLevel) const
{
    if (log4qtLevel == Log4Qt::Level::FATAL_INT) {
        return LogEntry::LogLevel::Critical;
    } else if (log4qtLevel == Log4Qt::Level::ERROR_INT) {
        return LogEntry::LogLevel::Error;
    } else if (log4qtLevel == Log4Qt::Level::WARN_INT) {
        return LogEntry::LogLevel::Warning;
    } else if (log4qtLevel == Log4Qt::Level::INFO_INT) {
        return LogEntry::LogLevel::Info;
    } else {
        return LogEntry::LogLevel::Debug;
    }
}

#endif // LOG4QT_AVAILABLE
