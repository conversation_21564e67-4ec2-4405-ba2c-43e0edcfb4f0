#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>
#include <QTableView>
#include <QVBoxLayout>
#include <QWidget>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 测试信号修复效果的程序
 */
class SignalTestWidget : public QWidget
{
    Q_OBJECT

public:
    SignalTestWidget(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        setupModel();
        connectSignals();
        testSignals();
    }

private slots:
    void onStandardDataChanged(const QModelIndex& topLeft, const QModelIndex& bottomRight)
    {
        qDebug() << "✅ 标准 dataChanged 信号触发!";
        qDebug() << "  topLeft:" << topLeft.row() << "," << topLeft.column();
        qDebug() << "  bottomRight:" << bottomRight.row() << "," << bottomRight.column();
        m_standardSignalCount++;
    }
    
    void onCustomModelDataChanged()
    {
        qDebug() << "✅ 自定义 modelDataChanged 信号触发!";
        m_customSignalCount++;
    }

private:
    void setupUI()
    {
        setWindowTitle("信号修复测试");
        resize(800, 600);
        
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        m_tableView = new QTableView(this);
        layout->addWidget(m_tableView);
    }
    
    void setupModel()
    {
        // 创建源模型
        m_sourceModel = new FileLogModel(this);
        
        // 创建代理模型
        m_proxyModel = new LogSortFilterProxyModel(this);
        m_proxyModel->setSourceModel(m_sourceModel);
        
        // 设置到视图
        m_tableView->setModel(m_proxyModel);
        
        qDebug() << "模型设置完成";
    }
    
    void connectSignals()
    {
        // 连接标准的 dataChanged 信号
        connect(m_sourceModel, QOverload<const QModelIndex&, const QModelIndex&>::of(&QAbstractItemModel::dataChanged),
                this, &SignalTestWidget::onStandardDataChanged);
        
        // 连接自定义的 modelDataChanged 信号
        connect(m_sourceModel, &BaseLogModel::modelDataChanged,
                this, &SignalTestWidget::onCustomModelDataChanged);
        
        qDebug() << "信号连接完成";
    }
    
    void testSignals()
    {
        qDebug() << "\n=== 开始信号测试 ===";
        
        m_standardSignalCount = 0;
        m_customSignalCount = 0;
        
        // 测试1：添加单个条目
        qDebug() << "\n测试1：添加单个条目";
        LogEntry entry1;
        entry1.setTimestamp(QDateTime::currentDateTime());
        entry1.setLevel(LogEntry::LogLevel::Info);
        entry1.setSource("TestSource");
        entry1.setMessage("测试消息1");
        entry1.setDetails("测试详情1");
        
        m_sourceModel->addLogEntry(entry1);
        
        qDebug() << QString("添加单个条目后 - 标准信号: %1, 自定义信号: %2")
                    .arg(m_standardSignalCount).arg(m_customSignalCount);
        
        // 测试2：批量添加条目
        qDebug() << "\n测试2：批量添加条目";
        QVector<LogEntry> entries;
        for (int i = 0; i < 3; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
            entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
            entry.setSource(QString("BatchSource%1").arg(i));
            entry.setMessage(QString("批量消息%1").arg(i));
            entry.setDetails(QString("批量详情%1").arg(i));
            entries.append(entry);
        }
        
        int beforeStandard = m_standardSignalCount;
        int beforeCustom = m_customSignalCount;
        
        m_sourceModel->addLogEntries(entries);
        
        qDebug() << QString("批量添加后 - 标准信号: %1 (+%2), 自定义信号: %3 (+%4)")
                    .arg(m_standardSignalCount).arg(m_standardSignalCount - beforeStandard)
                    .arg(m_customSignalCount).arg(m_customSignalCount - beforeCustom);
        
        // 检查视图状态
        qDebug() << "\n=== 检查视图状态 ===";
        qDebug() << QString("源模型: rowCount=%1, columnCount=%2")
                    .arg(m_sourceModel->rowCount())
                    .arg(m_sourceModel->columnCount());
        qDebug() << QString("代理模型: rowCount=%1, columnCount=%2")
                    .arg(m_proxyModel->rowCount())
                    .arg(m_proxyModel->columnCount());
        
        // 显示第一行数据
        if (m_proxyModel->rowCount() > 0) {
            qDebug() << "第一行数据:";
            for (int col = 0; col < m_proxyModel->columnCount(); ++col) {
                QModelIndex index = m_proxyModel->index(0, col);
                QVariant data = m_proxyModel->data(index, Qt::DisplayRole);
                qDebug() << QString("  列 %1: %2").arg(col).arg(data.toString());
            }
        }
        
        // 最终结果
        qDebug() << "\n=== 测试结果 ===";
        bool standardOK = m_standardSignalCount > 0;
        bool customOK = m_customSignalCount > 0;
        bool viewOK = m_proxyModel->rowCount() > 0;
        
        qDebug() << QString("标准 dataChanged 信号: %1 (%2)")
                    .arg(standardOK ? "✅ 正常" : "❌ 异常")
                    .arg(m_standardSignalCount);
        qDebug() << QString("自定义 modelDataChanged 信号: %1 (%2)")
                    .arg(customOK ? "✅ 正常" : "❌ 异常")
                    .arg(m_customSignalCount);
        qDebug() << QString("视图数据显示: %1 (行数: %2)")
                    .arg(viewOK ? "✅ 正常" : "❌ 异常")
                    .arg(m_proxyModel->rowCount());
        
        if (standardOK && customOK && viewOK) {
            qDebug() << "\n🎉 信号修复成功！所有测试通过！";
        } else {
            qDebug() << "\n❌ 信号修复可能有问题，需要进一步检查";
        }
    }

private:
    QTableView* m_tableView;
    FileLogModel* m_sourceModel;
    LogSortFilterProxyModel* m_proxyModel;
    
    int m_standardSignalCount = 0;
    int m_customSignalCount = 0;
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 信号修复测试程序 ===";
    
    SignalTestWidget widget;
    
    qDebug() << "\n=== 测试完成 ===";
    
    return 0;
}

#include "test_signal_fix.moc"
