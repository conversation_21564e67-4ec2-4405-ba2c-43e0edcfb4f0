#include <QCoreApplication>
#include <QTimer>
#include <QDebug>
#include "streamlined_log4qt_source.h"
#include "logger.h"

/**
 * @brief 测试StreamlinedLogAppender是否能正确捕获Log4QT输出
 */
class Log4QtCaptureTest : public QObject
{
    Q_OBJECT

public:
    Log4QtCaptureTest(QObject* parent = nullptr) : QObject(parent), m_receivedCount(0) {}

    void runTest()
    {
        qDebug() << "\n=== 开始测试StreamlinedLogAppender捕获Log4QT输出 ===";
        
        // 1. 初始化Log4QT
        qDebug() << "1. 初始化Log4QT...";
        Logger::init("./logs");  // 初始化Log4QT配置
        qDebug() << "✓ Log4QT初始化完成";
        
        // 2. 创建StreamlinedLog4QtSource
        qDebug() << "2. 创建StreamlinedLog4QtSource...";
        m_source = new StreamlinedLog4QtSource(this);
        
        // 连接信号以监听接收到的日志
        connect(m_source, &IDataSource::dataReady, 
                this, &Log4QtCaptureTest::onLogDataReceived);
        connect(m_source, &IDataSource::error,
                this, &Log4QtCaptureTest::onError);
        connect(m_source, &IDataSource::statusChanged,
                this, &Log4QtCaptureTest::onStatusChanged);
        
        // 设置logger名称为"root"（与Logger宏使用的名称一致）
        m_source->setLoggerName("root");
        qDebug() << "✓ StreamlinedLog4QtSource创建完成，Logger名称:" << m_source->getLoggerName();
        
        // 3. 连接到Log4QT
        qDebug() << "3. 连接到Log4QT...";
        bool connected = m_source->connectToSource();
        if (!connected) {
            qDebug() << "✗ 连接失败";
            QCoreApplication::exit(1);
            return;
        }
        qDebug() << "✓ 连接成功";
        
        // 4. 开始测试日志输出
        qDebug() << "4. 开始测试日志输出...";
        testLogOutput();
        
        // 5. 设置定时器检查结果
        QTimer::singleShot(2000, this, &Log4QtCaptureTest::checkResults);
    }

private slots:
    void onLogDataReceived(const QVector<LogEntry>& entries)
    {
        m_receivedCount += entries.size();
        qDebug() << QString("✓ 接收到 %1 条日志数据，总计: %2").arg(entries.size()).arg(m_receivedCount);
        
        for (const LogEntry& entry : entries) {
            qDebug() << QString("  - [%1] %2: %3")
                        .arg(entry.levelString())
                        .arg(entry.source())
                        .arg(entry.message());
        }
    }
    
    void onError(const QString& error)
    {
        qDebug() << "✗ 错误:" << error;
    }
    
    void onStatusChanged(const QString& status)
    {
        qDebug() << "状态:" << status;
    }
    
    void testLogOutput()
    {
        qDebug() << "开始输出测试日志...";

        // 使用Logger宏输出不同级别的日志
        qDebug() << "输出DEBUG日志...";
        DEBUG("这是一条DEBUG日志消息");

        qDebug() << "输出INFO日志...";
        INFO("这是一条INFO日志消息");

        qDebug() << "输出WARN日志...";
        WARN("这是一条WARN日志消息");

        qDebug() << "输出ERROR日志...";
        ERROR("这是一条ERROR日志消息");

        qDebug() << "输出FATAL日志...";
        FATAL("这是一条FATAL日志消息");

        // 也测试通信日志（使用commLogger）
        qDebug() << "输出通信日志...";
        DEBUG_COMM("这是一条通信DEBUG日志");
        INFO_COMM("这是一条通信INFO日志");

        qDebug() << "✓ 测试日志输出完成，等待StreamlinedLogAppender接收...";
    }
    
    void checkResults()
    {
        qDebug() << "\n=== 测试结果检查 ===";
        qDebug() << QString("总共接收到的日志条数: %1").arg(m_receivedCount);
        
        if (m_receivedCount > 0) {
            qDebug() << "✓ 测试成功！StreamlinedLogAppender能够正确捕获Log4QT输出";
        } else {
            qDebug() << "✗ 测试失败！StreamlinedLogAppender没有捕获到任何Log4QT输出";
            
            // 输出调试信息
            qDebug() << "\n调试信息：";
            qDebug() << "- Logger名称:" << m_source->getLoggerName();
            qDebug() << "- 连接状态:" << m_source->isConnected();
            qDebug() << "- 数据源信息:" << m_source->getSourceInfo();
        }
        
        qDebug() << "=== 测试完成 ===\n";
        QCoreApplication::exit(m_receivedCount > 0 ? 0 : 1);
    }

private:
    StreamlinedLog4QtSource* m_source;
    int m_receivedCount;
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("Log4QtCaptureTest");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("LogViewer");
    
    qDebug() << "Log4QT捕获测试程序启动";
    
    Log4QtCaptureTest test;
    QTimer::singleShot(100, &test, &Log4QtCaptureTest::runTest);
    
    return app.exec();
}

#include "test_log4qt_capture.moc"
