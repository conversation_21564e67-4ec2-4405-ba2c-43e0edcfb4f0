#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include <QProgressBar>
#include "simplelogviewer.h"

/**
 * @brief 清除功能卡死问题修复验证程序
 */
class ClearTestWidget : public QWidget
{
    Q_OBJECT

public:
    ClearTestWidget(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        setupLogViewer();
    }

private slots:
    void onCreateViewer()
    {
        if (m_logViewer) {
            delete m_logViewer;
        }
        
        m_logViewer = new SimpleLogViewer(SimpleLogViewer::FileViewer, this);
        m_viewerLayout->addWidget(m_logViewer);
        
        // 连接信号
        connect(m_logViewer, &SimpleLogViewer::dataLoaded,
                [this](int count) {
                    m_logOutput->append(QString("✓ 数据加载完成: %1 条").arg(count));
                    m_progressBar->setVisible(false);
                });
        
        connect(m_logViewer, &SimpleLogViewer::errorOccurred,
                [this](const QString& error) {
                    m_logOutput->append(QString("✗ 错误: %1").arg(error));
                    m_progressBar->setVisible(false);
                });
        
        m_logOutput->append("✓ 文件查看器创建成功");
    }
    
    void onTestClear()
    {
        if (!m_logViewer) {
            m_logOutput->append("✗ 请先创建查看器");
            return;
        }
        
        m_logOutput->append("=== 测试清除功能 ===");
        m_logOutput->append("正在执行清除操作...");
        
        // 记录开始时间
        m_clearStartTime = QDateTime::currentMSecsSinceEpoch();
        
        // 执行清除操作
        m_logViewer->clear();
        
        // 记录结束时间
        qint64 elapsedMs = QDateTime::currentMSecsSinceEpoch() - m_clearStartTime;
        
        m_logOutput->append(QString("✓ 清除操作完成，耗时: %1 毫秒").arg(elapsedMs));
        
        if (elapsedMs > 1000) {
            m_logOutput->append("⚠️ 警告：清除操作耗时较长，可能存在性能问题");
        } else {
            m_logOutput->append("✓ 清除操作性能正常");
        }
    }
    
    void onStressTest()
    {
        if (!m_logViewer) {
            m_logOutput->append("✗ 请先创建查看器");
            return;
        }
        
        m_logOutput->append("=== 压力测试：连续清除操作 ===");
        m_stressTestCount = 0;
        m_maxStressTests = 10;
        
        // 启动压力测试定时器
        QTimer* stressTimer = new QTimer(this);
        connect(stressTimer, &QTimer::timeout, [this, stressTimer]() {
            m_stressTestCount++;
            
            m_logOutput->append(QString("执行第 %1 次清除...").arg(m_stressTestCount));
            
            qint64 startTime = QDateTime::currentMSecsSinceEpoch();
            m_logViewer->clear();
            qint64 elapsedMs = QDateTime::currentMSecsSinceEpoch() - startTime;
            
            m_logOutput->append(QString("第 %1 次清除完成，耗时: %2 毫秒").arg(m_stressTestCount).arg(elapsedMs));
            
            if (m_stressTestCount >= m_maxStressTests) {
                stressTimer->stop();
                stressTimer->deleteLater();
                m_logOutput->append("✓ 压力测试完成，未发现卡死问题");
            }
        });
        
        stressTimer->start(500); // 每500毫秒执行一次
    }

private:
    void setupUI()
    {
        setWindowTitle("LogModel::clear() 卡死问题修复测试");
        setMinimumSize(800, 600);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        
        // 控制面板
        QWidget* controlPanel = new QWidget();
        QHBoxLayout* controlLayout = new QHBoxLayout(controlPanel);
        
        QPushButton* createBtn = new QPushButton("创建查看器");
        connect(createBtn, &QPushButton::clicked, this, &ClearTestWidget::onCreateViewer);
        controlLayout->addWidget(createBtn);
        
        QPushButton* clearBtn = new QPushButton("测试清除");
        connect(clearBtn, &QPushButton::clicked, this, &ClearTestWidget::onTestClear);
        controlLayout->addWidget(clearBtn);
        
        QPushButton* stressBtn = new QPushButton("压力测试");
        connect(stressBtn, &QPushButton::clicked, this, &ClearTestWidget::onStressTest);
        controlLayout->addWidget(stressBtn);
        
        controlLayout->addStretch();
        
        mainLayout->addWidget(controlPanel);
        
        // 进度条
        m_progressBar = new QProgressBar();
        m_progressBar->setVisible(false);
        mainLayout->addWidget(m_progressBar);
        
        // 查看器容器
        QWidget* viewerContainer = new QWidget();
        m_viewerLayout = new QVBoxLayout(viewerContainer);
        mainLayout->addWidget(viewerContainer, 2);
        
        // 测试输出
        m_logOutput = new QTextEdit();
        m_logOutput->setMaximumHeight(200);
        m_logOutput->setPlainText("=== LogModel::clear() 卡死问题修复测试 ===\n");
        m_logOutput->append("修复内容:");
        m_logOutput->append("1. 将squeeze()操作移到beginResetModel()之外");
        m_logOutput->append("2. 在clear()前停止异步文件读取操作");
        m_logOutput->append("3. 优化CircularLogBuffer的squeeze()逻辑");
        m_logOutput->append("");
        m_logOutput->append("测试步骤:");
        m_logOutput->append("1. 点击'创建查看器'");
        m_logOutput->append("2. 点击'测试清除'验证单次清除");
        m_logOutput->append("3. 点击'压力测试'验证连续清除");
        m_logOutput->append("");
        mainLayout->addWidget(m_logOutput);
    }
    
    void setupLogViewer()
    {
        m_logViewer = nullptr;
        m_clearStartTime = 0;
        m_stressTestCount = 0;
        m_maxStressTests = 10;
    }

private:
    SimpleLogViewer* m_logViewer;
    QVBoxLayout* m_viewerLayout;
    QTextEdit* m_logOutput;
    QProgressBar* m_progressBar;
    
    qint64 m_clearStartTime;
    int m_stressTestCount;
    int m_maxStressTests;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ClearTestWidget widget;
    widget.show();
    
    return app.exec();
}

#include "test_clear_fix.moc"
