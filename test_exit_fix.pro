QT += core gui widgets

CONFIG += c++17

TARGET = TestExitFix
TEMPLATE = app

# 定义宏
DEFINES += SIMPLE_VERSION
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

# 如果有Log4Qt，启用相关功能
# DEFINES += LOG4QT_AVAILABLE

HEADERS += \
    logviewer_global.h \
    logentry.h \
    idatasource.h \
    simplelog4qtdatasource.h \
    simplelogviewerappender.h \
    simpleconfigmanager.h \
    logger.h

SOURCES += \
    test_exit_fix.cpp \
    logentry.cpp \
    simplelog4qtdatasource.cpp \
    simplelogviewerappender.cpp \
    simpleconfigmanager.cpp \
    logger.cpp
