#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QPushButton>
#include <QTableView>
#include <QLabel>
#include <QDebug>
#include <QTimer>
#include <QHeaderView>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 简单的视图修复测试程序
 */
class TestViewFix : public QWidget
{
    Q_OBJECT

public:
    explicit TestViewFix(QWidget* parent = nullptr)
        : QWidget(parent)
    {
        setupUI();
        setupModel();
        
        setWindowTitle("视图修复测试");
        resize(800, 600);
    }

private slots:
    void addTestData()
    {
        qDebug() << "=== 开始添加测试数据 ===";
        
        QVector<LogEntry> entries;
        for (int i = 0; i < 10; ++i) {
            LogEntry entry(
                QDateTime::currentDateTime().addSecs(i),
                static_cast<LogEntry::LogLevel>(i % 5),
                QString("Source%1").arg(i),
                QString("测试消息 %1 - 这是一条测试日志").arg(i),
                QString("详细信息 %1").arg(i)
            );
            entries.append(entry);
        }
        
        qDebug() << "准备添加" << entries.size() << "条数据";
        qDebug() << "添加前 - 模型行数:" << m_model->rowCount() << "，代理行数:" << m_proxyModel->rowCount();
        
        // 添加数据
        m_model->addLogEntries(entries);
        
        qDebug() << "添加后 - 模型行数:" << m_model->rowCount() << "，代理行数:" << m_proxyModel->rowCount();
        
        // 强制刷新
        m_proxyModel->invalidateFilter();
        m_tableView->update();
        
        // 延迟检查
        QTimer::singleShot(100, [this]() {
            qDebug() << "延迟检查 - 模型行数:" << m_model->rowCount() << "，代理行数:" << m_proxyModel->rowCount();
            qDebug() << "视图行数:" << m_tableView->model()->rowCount();
            
            m_statusLabel->setText(QString("数据: 模型%1行，代理%2行，视图%3行")
                                  .arg(m_model->rowCount())
                                  .arg(m_proxyModel->rowCount())
                                  .arg(m_tableView->model()->rowCount()));
        });
        
        qDebug() << "=== 测试数据添加完成 ===";
    }
    
    void clearData()
    {
        qDebug() << "清除数据";
        m_model->clear();
        m_statusLabel->setText("数据已清除");
    }
    
    void checkData()
    {
        qDebug() << "=== 检查数据状态 ===";
        qDebug() << "FileLogModel 行数:" << m_model->rowCount();
        qDebug() << "ProxyModel 行数:" << m_proxyModel->rowCount();
        qDebug() << "TableView 行数:" << m_tableView->model()->rowCount();
        
        // 检查前几行数据
        for (int i = 0; i < qMin(3, m_model->rowCount()); ++i) {
            LogEntry entry = m_model->getLogEntry(i);
            qDebug() << QString("行 %1: %2").arg(i).arg(entry.message());
        }
        
        // 检查代理模型设置
        qDebug() << "代理模型过滤模式:" << m_proxyModel->filterPattern();
        qDebug() << "代理模型显示Info级别:" << m_proxyModel->isLevelVisible(LogEntry::LogLevel::Info);
        
        m_statusLabel->setText(QString("检查完成 - 模型:%1，代理:%2，视图:%3")
                              .arg(m_model->rowCount())
                              .arg(m_proxyModel->rowCount())
                              .arg(m_tableView->model()->rowCount()));
    }

private:
    void setupUI()
    {
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // 按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        QPushButton* addBtn = new QPushButton("添加测试数据");
        QPushButton* clearBtn = new QPushButton("清除数据");
        QPushButton* checkBtn = new QPushButton("检查数据");
        
        buttonLayout->addWidget(addBtn);
        buttonLayout->addWidget(clearBtn);
        buttonLayout->addWidget(checkBtn);
        buttonLayout->addStretch();
        
        connect(addBtn, &QPushButton::clicked, this, &TestViewFix::addTestData);
        connect(clearBtn, &QPushButton::clicked, this, &TestViewFix::clearData);
        connect(checkBtn, &QPushButton::clicked, this, &TestViewFix::checkData);
        
        // 表格
        m_tableView = new QTableView();
        m_tableView->setAlternatingRowColors(true);
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        
        // 状态标签
        m_statusLabel = new QLabel("准备就绪");
        
        layout->addLayout(buttonLayout);
        layout->addWidget(m_tableView, 1);
        layout->addWidget(m_statusLabel);
    }
    
    void setupModel()
    {
        // 创建模型
        m_model = new FileLogModel(10000, this);
        
        // 创建代理模型
        m_proxyModel = new LogSortFilterProxyModel(this);
        m_proxyModel->setSourceModel(m_model);
        
        // 设置到视图
        m_tableView->setModel(m_proxyModel);
        
        // 设置列宽
        QHeaderView* header = m_tableView->horizontalHeader();
        header->resizeSection(0, 180); // 时间戳
        header->resizeSection(1, 80);  // 级别
        header->resizeSection(2, 120); // 来源
        header->resizeSection(3, 200); // 消息
        header->setStretchLastSection(true); // 详情列拉伸
        
        // 连接信号
        connect(m_model, &FileLogModel::modelDataChanged, [this]() {
            qDebug() << "模型数据变化信号 - 当前行数:" << m_model->rowCount();
        });
        
        qDebug() << "模型设置完成";
    }

private:
    FileLogModel* m_model;
    LogSortFilterProxyModel* m_proxyModel;
    QTableView* m_tableView;
    QLabel* m_statusLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 注册元类型
    qRegisterMetaType<LogEntry>("LogEntry");
    qRegisterMetaType<QVector<LogEntry>>("QVector<LogEntry>");
    qRegisterMetaType<LogEntry::LogLevel>("LogEntry::LogLevel");
    
    TestViewFix window;
    window.show();
    
    return app.exec();
}

#include "test_view_fix.moc"
