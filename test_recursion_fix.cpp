#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "logger.h"
#include "streamlined_log4qt_source.h"

/**
 * @brief 测试递归循环修复
 * 
 * 这个测试专门验证我们是否修复了qDebug()递归循环问题
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "\n=== 测试递归循环修复 ===";
    qDebug() << "问题：StreamlinedLogAppender::append()中的qDebug()调用";
    qDebug() << "会被Log4Qt捕获，形成无限递归循环";
    qDebug() << "修复：移除所有可能导致递归的qDebug()调用\n";
    
    // 1. 初始化Log4QT
    qDebug() << "1. 初始化Log4QT...";
    Logger::init("./logs");
    qDebug() << "✓ Log4QT初始化完成";
    
    // 2. 创建StreamlinedLog4QtSource
    qDebug() << "2. 创建StreamlinedLog4QtSource...";
    StreamlinedLog4QtSource source;
    
    // 3. 设置信号监听
    bool logReceived = false;
    int receivedCount = 0;
    
    QObject::connect(&source, &IDataSource::dataReady, 
                     [&](const QVector<LogEntry>& entries) {
        logReceived = true;
        receivedCount += entries.size();
        qDebug() << QString("🎉 成功接收到 %1 条日志！递归问题已修复！").arg(entries.size());
        
        for (const LogEntry& entry : entries) {
            qDebug() << QString("  ✓ [%1] %2: %3")
                        .arg(entry.levelString())
                        .arg(entry.source())
                        .arg(entry.message());
        }
    });
    
    QObject::connect(&source, &IDataSource::error,
                     [](const QString& error) {
        qDebug() << "✗ 错误:" << error;
    });
    
    // 4. 连接到Log4QT
    qDebug() << "3. 连接到Log4QT...";
    source.setLoggerName("root");
    bool connected = source.connectToSource();
    qDebug() << "连接结果:" << (connected ? "✓ 成功" : "✗ 失败");
    
    if (!connected) {
        qDebug() << "连接失败，退出测试";
        return 1;
    }
    
    // 5. 输出测试日志
    QTimer::singleShot(500, [&]() {
        qDebug() << "\n4. 输出测试日志（检查是否会导致递归）...";
        
        // 这些调用之前会导致递归循环
        DEBUG("测试DEBUG - 检查递归修复");
        INFO("测试INFO - 检查递归修复");
        WARN("测试WARN - 检查递归修复");
        ERROR("测试ERROR - 检查递归修复");
        
        qDebug() << "✓ 日志输出完成，等待接收...\n";
    });
    
    // 6. 检查结果
    QTimer::singleShot(3000, [&]() {
        qDebug() << "\n=== 测试结果 ===";
        
        if (logReceived && receivedCount > 0) {
            qDebug() << "🎉 测试成功！";
            qDebug() << QString("✓ 接收到 %1 条日志").arg(receivedCount);
            qDebug() << "✓ 递归循环问题已修复";
            qDebug() << "✓ StreamlinedLogAppender正常工作";
            qDebug() << "✓ 没有发生无限递归";
        } else {
            qDebug() << "❌ 测试失败！";
            qDebug() << "可能的原因：";
            qDebug() << "- Log4Qt配置问题";
            qDebug() << "- Appender未正确激活";
            qDebug() << "- 日志级别过滤问题";
            qDebug() << "- 仍存在其他问题";
        }
        
        app.quit();
    });
    
    return app.exec();
}
