#ifndef SIMPLE_ASYNC_FILE_READER_H
#define SIMPLE_ASYNC_FILE_READER_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QStringList>
#include <QDateTime>

/**
 * @brief 简化的异步文件读取器
 * 
 * 单一类实现多线程文件读取，去除不必要的复杂性
 * 功能：
 * - 后台线程读取文件
 * - 分块返回文本行
 * - 进度更新
 * - 支持取消操作
 */
class SimpleAsyncFileReader : public QObject
{
    Q_OBJECT

public:
    enum Status {
        Idle,
        Reading,
        Cancelled,
        Completed,
        Error
    };

    explicit SimpleAsyncFileReader(QObject* parent = nullptr);
    ~SimpleAsyncFileReader();

    // 开始读取文件
    void startReading(const QString& filePath, int maxLines = 0);
    
    // 取消读取
    void cancelReading();
    
    // 获取状态
    Status getStatus() const;
    
    // 设置分块大小（默认1000行）
    void setChunkSize(int size);

signals:
    // 开始读取信号
    void readingStarted(const QString& filePath, int estimatedLines);
    
    // 进度更新信号
    void progressUpdated(int processedLines, int totalLines, int percentage);
    
    // 数据块就绪信号 - 直接返回字符串列表
    void dataChunkReady(const QStringList& lines, bool isLastChunk);
    
    // 完成信号
    void readingCompleted(int totalLines, qint64 elapsedMs);
    
    // 取消信号
    void readingCancelled();
    
    // 错误信号
    void errorOccurred(const QString& error);

private slots:
    void doReading();

private:
    // 估算文件行数
    int estimateLineCount(const QString& filePath) const;

private:
    QThread* m_workerThread;
    mutable QMutex m_mutex;
    
    // 状态
    Status m_status;
    
    // 参数
    QString m_filePath;
    int m_maxLines;
    int m_chunkSize;
    
    // 进度
    int m_processedLines;
    int m_totalLines;
    qint64 m_startTime;
};

#endif // SIMPLE_ASYNC_FILE_READER_H
