#include "filelogmodel.h"
#include "logentry.h"
#include <QApplication>
#include <QTableView>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>
#include <QDateTime>
#include <QTimer>

/**
 * @brief 测试FileLogModel修复后的显示功能
 */
class TestWidget : public QWidget
{
    Q_OBJECT

public:
    TestWidget(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        setupModel();
        loadTestData();
    }

private slots:
    void onModelDataChanged()
    {
        qDebug() << "Model数据已更新，总数:" << m_model->getTotalCount();
    }

private:
    void setupUI()
    {
        setWindowTitle("FileLogModel显示测试");
        resize(800, 600);
        
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        m_tableView = new QTableView(this);
        layout->addWidget(m_tableView);
    }
    
    void setupModel()
    {
        m_model = new FileLogModel(this);
        m_tableView->setModel(m_model);
        
        // 连接信号
        connect(m_model, &BaseLogModel::dataChanged,
                this, &TestWidget::onModelDataChanged);
        
        qDebug() << "Model设置完成";
    }
    
    void loadTestData()
    {
        QVector<LogEntry> testEntries;
        
        // 创建测试数据
        for (int i = 0; i < 100; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
            entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
            entry.setSource(QString("TestSource%1").arg(i % 10));
            entry.setMessage(QString("测试消息 %1 - 这是一条测试日志消息").arg(i));
            entry.setDetails(QString("详细信息 %1 - 包含更多详细内容").arg(i));
            testEntries.append(entry);
        }
        
        qDebug() << "准备添加" << testEntries.size() << "条测试数据";
        
        // 延迟添加数据，模拟真实场景
        QTimer::singleShot(1000, [this, testEntries]() {
            m_model->addLogEntries(testEntries);
            qDebug() << "测试数据已添加到Model";
        });
    }

private:
    QTableView* m_tableView;
    FileLogModel* m_model;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestWidget widget;
    widget.show();
    
    return app.exec();
}

#include "test_filelogmodel_fix.moc"
