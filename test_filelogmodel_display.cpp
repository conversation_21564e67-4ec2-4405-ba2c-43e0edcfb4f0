#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QTableView>
#include <QHeaderView>
#include <QPushButton>
#include <QLabel>
#include <QDebug>
#include <QTimer>
#include <QDateTime>

#include "filelogmodel.h"
#include "logentry.h"

/**
 * @brief 测试 FileLogModel 数据显示的程序
 * 
 * 用于诊断 FileLogModel 在 QTableView 中不能正确显示数据的问题
 */
class FileLogModelDisplayTest : public QWidget
{
    Q_OBJECT

public:
    explicit FileLogModelDisplayTest(QWidget* parent = nullptr)
        : QWidget(parent)
        , m_model(nullptr)
        , m_tableView(nullptr)
        , m_statusLabel(nullptr)
        , m_addDataButton(nullptr)
        , m_clearDataButton(nullptr)
    {
        setupUI();
        setupModel();
        connectSignals();
        
        // 延迟添加测试数据
        QTimer::singleShot(1000, this, &FileLogModelDisplayTest::addTestData);
    }

private slots:
    void addTestData()
    {
        qDebug() << "=== 开始添加测试数据 ===";
        
        QVector<LogEntry> testEntries;
        
        // 创建不同级别的测试数据
        for (int i = 0; i < 10; ++i) {
            LogEntry entry;
            entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
            entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
            entry.setSource(QString("TestSource%1").arg(i));
            entry.setMessage(QString("测试消息 %1").arg(i));
            entry.setDetails(QString("详细信息 %1").arg(i));
            
            testEntries.append(entry);
        }
        
        qDebug() << "创建了" << testEntries.size() << "条测试数据";
        
        // 添加到模型
        m_model->addLogEntries(testEntries);
        
        // 检查模型状态
        checkModelStatus();
        
        qDebug() << "=== 测试数据添加完成 ===";
    }
    
    void onAddDataClicked()
    {
        addTestData();
    }
    
    void onClearDataClicked()
    {
        qDebug() << "=== 清除数据 ===";
        m_model->clear();
        checkModelStatus();
    }
    
    void onModelDataChanged()
    {
        qDebug() << "Model dataChanged 信号触发";
        checkModelStatus();
        
        // 强制刷新表格
        m_tableView->reset();
        m_tableView->update();
    }
    
    void checkModelStatus()
    {
        if (!m_model) {
            qWarning() << "Model 为空";
            return;
        }
        
        int rowCount = m_model->rowCount();
        int columnCount = m_model->columnCount();
        int totalCount = m_model->getTotalCount();
        qint64 memoryUsage = m_model->getMemoryUsage();
        
        qDebug() << "=== Model 状态检查 ===";
        qDebug() << "rowCount():" << rowCount;
        qDebug() << "columnCount():" << columnCount;
        qDebug() << "getTotalCount():" << totalCount;
        qDebug() << "getMemoryUsage():" << memoryUsage << "bytes";
        
        // 检查列可见性
        for (int col = 0; col < static_cast<int>(BaseLogModel::ColumnCount); ++col) {
            bool visible = m_model->isColumnVisible(static_cast<BaseLogModel::Column>(col));
            qDebug() << "列" << col << "可见性:" << visible;
        }
        
        // 检查前几行数据
        for (int row = 0; row < qMin(3, rowCount); ++row) {
            qDebug() << "=== 行" << row << "数据 ===";
            for (int col = 0; col < columnCount; ++col) {
                QModelIndex index = m_model->index(row, col);
                QVariant data = m_model->data(index, Qt::DisplayRole);
                qDebug() << "  列" << col << ":" << data.toString();
            }
        }
        
        // 更新状态标签
        QString statusText = QString("行数: %1, 列数: %2, 总数: %3, 内存: %4 KB")
                            .arg(rowCount)
                            .arg(columnCount)
                            .arg(totalCount)
                            .arg(memoryUsage / 1024);
        m_statusLabel->setText(statusText);
        
        qDebug() << "=== 状态检查完成 ===";
    }

private:
    void setupUI()
    {
        setWindowTitle("FileLogModel 数据显示测试");
        resize(1000, 600);
        
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // 状态标签
        m_statusLabel = new QLabel("状态: 未初始化", this);
        layout->addWidget(m_statusLabel);
        
        // 按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        m_addDataButton = new QPushButton("添加测试数据", this);
        m_clearDataButton = new QPushButton("清除数据", this);
        buttonLayout->addWidget(m_addDataButton);
        buttonLayout->addWidget(m_clearDataButton);
        buttonLayout->addStretch();
        layout->addLayout(buttonLayout);
        
        // 表格视图
        m_tableView = new QTableView(this);
        m_tableView->setAlternatingRowColors(true);
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_tableView->setSortingEnabled(false); // 禁用排序以简化测试
        m_tableView->verticalHeader()->setVisible(false);
        
        layout->addWidget(m_tableView);
        
        qDebug() << "UI 设置完成";
    }
    
    void setupModel()
    {
        m_model = new FileLogModel(1000, this); // 设置较小的容量用于测试
        m_tableView->setModel(m_model);
        
        // 设置表格列宽
        QHeaderView* header = m_tableView->horizontalHeader();
        header->setStretchLastSection(true);
        header->resizeSection(BaseLogModel::TimestampColumn, 180);
        header->resizeSection(BaseLogModel::LevelColumn, 80);
        header->resizeSection(BaseLogModel::SourceColumn, 120);
        header->resizeSection(BaseLogModel::MessageColumn, 200);
        
        qDebug() << "Model 设置完成，类型:" << m_model->getModelType();
        checkModelStatus();
    }
    
    void connectSignals()
    {
        // 连接模型信号
        connect(m_model, &BaseLogModel::modelDataChanged,
                this, &FileLogModelDisplayTest::onModelDataChanged);
        
        // 连接按钮信号
        connect(m_addDataButton, &QPushButton::clicked,
                this, &FileLogModelDisplayTest::onAddDataClicked);
        connect(m_clearDataButton, &QPushButton::clicked,
                this, &FileLogModelDisplayTest::onClearDataClicked);
        
        qDebug() << "信号连接完成";
    }

private:
    FileLogModel* m_model;
    QTableView* m_tableView;
    QLabel* m_statusLabel;
    QPushButton* m_addDataButton;
    QPushButton* m_clearDataButton;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "=== FileLogModel 数据显示测试程序启动 ===";
    
    FileLogModelDisplayTest test;
    test.show();
    
    return app.exec();
}

#include "test_filelogmodel_display.moc"
