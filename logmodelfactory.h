#ifndef LOGMODELFACTORY_H
#define LOGMODELFACTORY_H

#include "logviewer_global.h"
#include "baselogmodel.h"
#include <memory>
#include <QString>

/**
 * @brief 日志Model工厂类
 * 
 * 根据使用场景创建合适的LogModel实例
 * 支持多种创建方式：按类型、按数据源、按场景
 */
class LOGVIEWER_EXPORT LogModelFactory
{
public:
    /**
     * @brief Model类型枚举
     */
    enum ModelType {
        CircularModel,  ///< 环形缓冲区Model（实时流数据）
        FileModel       ///< 文件Model（完整存储）
    };

    /**
     * @brief 数据源类型枚举
     */
    enum DataSourceType {
        Log4QtSource,       ///< Log4Qt实时日志源
        FileSource,         ///< 文件日志源
        NetworkSource,      ///< 网络日志源
        DatabaseSource,     ///< 数据库日志源
        UnknownSource       ///< 未知类型
    };

    /**
     * @brief 根据类型创建Model
     * @param type Model类型
     * @param capacity 容量（-1表示使用默认值）
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createModel(ModelType type, int capacity = -1);
    
    /**
     * @brief 根据数据源类型创建Model
     * @param sourceType 数据源类型字符串
     * @param capacity 容量（-1表示使用默认值）
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createModelForDataSource(const QString& sourceType, int capacity = -1);
    
    /**
     * @brief 根据数据源类型枚举创建Model
     * @param sourceType 数据源类型枚举
     * @param capacity 容量（-1表示使用默认值）
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createModelForDataSource(DataSourceType sourceType, int capacity = -1);
    
    /**
     * @brief 根据使用场景创建Model
     * @param isRealTimeStream 是否为实时流数据
     * @param capacity 容量（-1表示使用默认值）
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createModelForScenario(bool isRealTimeStream, int capacity = -1);
    
    /**
     * @brief 根据数据特征自动选择Model类型
     * @param expectedDataSize 预期数据大小
     * @param isRealTime 是否实时数据
     * @param needsFullHistory 是否需要完整历史
     * @param capacity 容量（-1表示使用默认值）
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createModelAuto(
        int expectedDataSize, 
        bool isRealTime, 
        bool needsFullHistory, 
        int capacity = -1
    );
    
    /**
     * @brief 解析数据源类型字符串
     * @param sourceTypeString 数据源类型字符串
     * @return 数据源类型枚举
     */
    static DataSourceType parseDataSourceType(const QString& sourceTypeString);
    
    /**
     * @brief 获取推荐的Model类型
     * @param sourceType 数据源类型
     * @return 推荐的Model类型
     */
    static ModelType getRecommendedModelType(DataSourceType sourceType);
    
    /**
     * @brief 获取默认容量
     * @param type Model类型
     * @return 默认容量
     */
    static int getDefaultCapacity(ModelType type);
    
    /**
     * @brief 获取Model类型名称
     * @param type Model类型
     * @return 类型名称
     */
    static QString getModelTypeName(ModelType type);
    
    /**
     * @brief 获取数据源类型名称
     * @param sourceType 数据源类型
     * @return 类型名称
     */
    static QString getDataSourceTypeName(DataSourceType sourceType);
    
    /**
     * @brief 验证容量参数
     * @param capacity 容量值
     * @param type Model类型
     * @return 验证后的容量值
     */
    static int validateCapacity(int capacity, ModelType type);

private:
    LogModelFactory() = default; // 工具类，不允许实例化
    
    /**
     * @brief 创建CircularLogModel实例
     * @param capacity 容量
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createCircularModel(int capacity);
    
    /**
     * @brief 创建FileLogModel实例
     * @param capacity 容量
     * @return Model实例
     */
    static std::unique_ptr<BaseLogModel> createFileModel(int capacity);
};

#endif // LOGMODELFACTORY_H
