#include "simplelogviewerappender.h"
#include <QDateTime>
#include <QDebug>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/level.h>
#include <log4qt/logger.h>
#include <log4qt/ndc.h>
#include <log4qt/mdc.h>
#endif

SimpleLogViewerAppender::SimpleLogViewerAppender(QObject* parent)
#ifdef LOG4QT_AVAILABLE
    : Log4Qt::AppenderSkeleton(parent)
#else
    : QObject(parent)
#endif
    , m_enabled(true)
    , m_totalEntryCount(0)
{
    qDebug() << "SimpleLogViewerAppender created";
}

SimpleLogViewerAppender::~SimpleLogViewerAppender()
{
    qDebug() << "SimpleLogViewerAppender destroyed, total entries processed:" << m_totalEntryCount;
}

void SimpleLogViewerAppender::appendLogEntry(const LogEntry& entry)
{
    if (!m_enabled) {
        return;
    }
    
    m_totalEntryCount++;
    
    // 发出新日志条目信号
    emit newLogEntry(entry);
    
    qDebug() << "Log entry appended:" << entry.message();
}

void SimpleLogViewerAppender::generateTestEntries(int count)
{
    if (!m_enabled) {
        qDebug() << "Appender is disabled, cannot generate test entries";
        return;
    }
    
    qDebug() << "Generating" << count << "test entries";
    
    for (int i = 0; i < count; ++i) {
        LogEntry entry = createTestEntry(i);
        appendLogEntry(entry);
    }
    
    qDebug() << "Test entries generation completed";
}

void SimpleLogViewerAppender::setEnabled(bool enabled)
{
    if (m_enabled != enabled) {
        m_enabled = enabled;
        qDebug() << "SimpleLogViewerAppender enabled:" << enabled;
    }
}

LogEntry SimpleLogViewerAppender::createTestEntry(int index) const
{
    // 创建测试日志条目
    QDateTime timestamp = QDateTime::currentDateTime().addSecs(-100 + index);
    
    // 循环使用不同的日志级别
    LogEntry::LogLevel levels[] = {
        LogEntry::LogLevel::Debug,
        LogEntry::LogLevel::Info,
        LogEntry::LogLevel::Warning,
        LogEntry::LogLevel::Error,
        LogEntry::LogLevel::Critical
    };
    LogEntry::LogLevel level = levels[index % 5];
    
    // 测试消息
    QStringList messages = {
        "系统启动完成",
        "用户登录成功",
        "数据库连接建立",
        "处理请求超时",
        "内存使用率过高",
        "文件读取失败",
        "网络连接中断",
        "配置文件加载完成",
        "缓存清理完成",
        "服务正常运行"
    };
    
    QString message = QString("[测试] %1").arg(messages[index % messages.size()]);
    QString source = QString("SimpleAppender:%1").arg(index);
    QString details = QString("测试条目 #%1").arg(index + 1);
    
    return LogEntry(timestamp, level, source, message, details);
}

#ifdef LOG4QT_AVAILABLE
// ========== Log4Qt Appender 接口实现 ==========

void SimpleLogViewerAppender::append(const Log4Qt::LoggingEvent& event)
{
    if (!m_enabled) {
        return;
    }

    // 转换Log4Qt事件为LogEntry
    LogEntry entry = convertToLogEntry(event);

    // 更新计数器
    m_totalEntryCount++;

    // 发出新日志条目信号
    emit newLogEntry(entry);

    qDebug() << "Log4Qt event received and converted:" << entry.message();
}

bool SimpleLogViewerAppender::requiresLayout() const
{
    // 我们不使用布局，因为我们直接处理LoggingEvent对象
    return false;
}

LogEntry SimpleLogViewerAppender::convertToLogEntry(const Log4Qt::LoggingEvent& event) const
{
    // 转换时间戳 - Log4Qt的timeStamp()返回qint64，需要转换为QDateTime
    QDateTime timestamp = QDateTime::fromMSecsSinceEpoch(event.timeStamp());

    // 转换日志级别 - 使用简化的方法
    LogEntry::LogLevel level = LogEntry::LogLevel::Info; // 默认级别
    int levelInt = 20000; // 默认INFO级别的数值

    try {
        Log4Qt::Level log4qtLevel = event.level();
        levelInt = log4qtLevel.toInt();

        // 根据Log4Qt级别值进行转换
        if (levelInt <= 10000) {          // DEBUG级别
            level = LogEntry::LogLevel::Debug;
        } else if (levelInt <= 20000) {   // INFO级别
            level = LogEntry::LogLevel::Info;
        } else if (levelInt <= 30000) {   // WARN级别
            level = LogEntry::LogLevel::Warning;
        } else if (levelInt <= 40000) {   // ERROR级别
            level = LogEntry::LogLevel::Error;
        } else {                          // FATAL级别
            level = LogEntry::LogLevel::Critical;
        }
    } catch (...) {
        // 如果级别转换失败，使用默认级别
        qWarning() << "Failed to convert Log4Qt level, using default INFO level";
        levelInt = 20000; // 重置为默认值
    }

    // 获取日志器名称作为来源
    QString source = "unknown";
    try {
        if (event.logger()) {
            source = event.logger()->name();
        }
        if (source.isEmpty()) {
            source = "root";
        }
    } catch (...) {
        source = "root";
    }

    // 获取消息内容
    QString message = "empty message";
    try {
        message = event.message();
        if (message.isEmpty()) {
            message = "[Empty Log Message]";
        }
    } catch (...) {
        message = "[Failed to get message]";
    }

    QString detailsStr = message;

    return LogEntry(timestamp, level, source, message, detailsStr);
}

QString SimpleLogViewerAppender::logLevelToString(LogEntry::LogLevel level) const
{
    switch (level) {
        case LogEntry::LogLevel::Debug: return "Debug";
        case LogEntry::LogLevel::Info: return "Info";
        case LogEntry::LogLevel::Warning: return "Warning";
        case LogEntry::LogLevel::Error: return "Error";
        case LogEntry::LogLevel::Critical: return "Critical";
        default: return "Unknown";
    }
}

QString SimpleLogViewerAppender::getRelativeTimeString(const QDateTime& timestamp) const
{
    QDateTime now = QDateTime::currentDateTime();
    qint64 secondsAgo = timestamp.secsTo(now);

    if (secondsAgo < 60) {
        return QString("%1 seconds ago").arg(secondsAgo);
    } else if (secondsAgo < 3600) {
        return QString("%1 minutes ago").arg(secondsAgo / 60);
    } else if (secondsAgo < 86400) {
        return QString("%1 hours ago").arg(secondsAgo / 3600);
    } else {
        return QString("%1 days ago").arg(secondsAgo / 86400);
    }
}

#endif // LOG4QT_AVAILABLE
