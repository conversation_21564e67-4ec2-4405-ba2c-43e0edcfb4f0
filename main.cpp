#include <QApplication>
#include "simple_test.h"
#include "logentry.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 注册自定义类型用于跨线程信号传递（必须在主线程中，在任何跨线程操作之前）
    // 设置应用程序信息
    app.setApplicationName("LogViewer Phase1 Test");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("LogViewer");

    qDebug() << "Starting LogViewer Phase1 Test Application";
    qDebug() << "Meta types registered for cross-thread signal transmission";

    // 创建测试窗口
    SimpleTestWidget window;
    window.show();

    return app.exec();
}
