@echo off
echo 正在编译Log4QT捕获测试程序...

REM 清理之前的构建文件
if exist test_log4qt_capture.exe del test_log4qt_capture.exe
if exist Makefile del Makefile
if exist *.o del *.o

REM 生成Makefile
qmake test_log4qt_capture.pro
if errorlevel 1 (
    echo 错误：qmake失败
    pause
    exit /b 1
)

REM 编译
nmake
if errorlevel 1 (
    echo 错误：编译失败
    pause
    exit /b 1
)

echo 编译成功！

REM 创建日志目录
if not exist logs mkdir logs

echo 正在运行测试...
echo.
test_log4qt_capture.exe

echo.
echo 测试完成！
pause
