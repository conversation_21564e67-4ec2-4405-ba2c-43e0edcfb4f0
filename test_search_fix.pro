QT += core widgets

CONFIG += c++17
CONFIG += console

TARGET = test_search_fix
TEMPLATE = app

# 定义宏
DEFINES += SIMPLE_VERSION
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

# 包含路径
INCLUDEPATH += .

# 源文件
SOURCES += \
    test_search_fix.cpp \
    simplelogviewer.cpp \
    logmodel.cpp \
    logsortfilterproxymodel.cpp \
    logentry.cpp \
    simpleconfigmanager.cpp \
    simplefiledatasource.cpp \
    filterexpression.cpp \
    circularlogbuffer.cpp \
    asyncfilereader.cpp

# 头文件
HEADERS += \
    simplelogviewer.h \
    logmodel.h \
    logsortfilterproxymodel.h \
    logentry.h \
    logviewer_global.h \
    simpleconfigmanager.h \
    simplefiledatasource.h \
    idatasource.h \
    filterexpression.h \
    circularlogbuffer.h \
    asyncfilereader.h
