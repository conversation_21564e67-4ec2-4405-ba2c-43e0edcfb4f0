
#ifndef ASYNCFILEREADER_WORKER_H
#define ASYNCFILEREADER_WORKER_H

#include "logentry.h"
#include <QObject>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QVector>
#include <QString>

// 内部Worker类，执行实际读取任务
class FileReadWorker : public QObject {
    Q_OBJECT
public:
    explicit FileReadWorker(QObject* parent = nullptr);

public slots:
    void doRead(const QString& filePath, const QString& encoding, int chunkSize, int maxEntries);
    void pause();
    void resume();
    void cancel();

signals:
    void chunkReady(const QVector<LogEntry>& entries, bool isLast);
    void progress(int processed, int estimated, int percent);
    void finished(int totalEntries, qint64 elapsedMs);
    void error(const QString& message);

private:
    QTextCodec* detectFileEncoding(const QString& filePath) const;
    LogEntry parseLogLine(const QString& line, int lineNumber) const;
    LogEntry::LogLevel detectLogLevel(const QString& text) const;
    QDateTime parseTimestamp(const QString& text) const;

private:
    bool m_paused;
    bool m_cancelled;
    QMutex m_mutex;
    QWaitCondition m_pauseCond;
};

// 对外AsyncFileReader接口
class AsyncFileReader : public QObject {
    Q_OBJECT
public:
    explicit AsyncFileReader(QObject* parent = nullptr);
    ~AsyncFileReader();

    void startReading(const QString& filePath, const QString& encoding = "UTF-8", int maxEntries = 0);
    void pauseReading();
    void resumeReading();
    void cancelReading();

    // 状态查询方法
    bool isReading() const;
    void setChunkSize(int chunkSize);

signals:
    void readingStarted(const QString& filePath, int estimatedLines);
    void progressUpdated(int processedLines, int totalLines, int percentage);
    void dataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk);
    void readingCompleted(int totalEntries, qint64 elapsedMs);
    void errorOccurred(const QString& error);

private:
    int estimateLineCount(const QString& filePath) const;

private:
    QThread m_workerThread;
    FileReadWorker* m_worker;
    int m_chunkSize;
    bool m_isReading;
};

#endif // ASYNCFILEREADER_WORKER_H
