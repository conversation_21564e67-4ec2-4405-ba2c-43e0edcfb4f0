#ifndef LOGENTRY_H
#define LOGENTRY_H

#include "logviewer_global.h"
#include <QDateTime>
#include <QString>
#include <QVariant>
#include <QSet>
#include <QMetaType>

class LOGVIEWER_EXPORT LogEntry
{
public:
    enum class LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    };

    LogEntry();
    LogEntry(const QDateTime& timestamp,
             LogLevel         level,
             const QString&   source,
             const QString&   message,
             const QString&   details = QString());

    QDateTime timestamp() const;
    LogLevel  level() const;
    QString   levelString() const;
    QString   source() const;
    QString   message() const;
    QString   details() const;

    bool operator<(const LogEntry& other) const;
    bool matchesFilter(const QString& filter) const;

    QVariant        toVariant() const;
    static LogEntry fromVariant(const QVariant& variant);

private:
    QDateTime _timestamp;
    LogLevel  _level;
    QString   _source;
    QString   _message;
    QString   _details;
};

inline uint qHash(const LogEntry::LogLevel& level, uint seed = 0) { return ::qHash(static_cast< int >(level), seed); }

Q_DECLARE_METATYPE(LogEntry)
Q_DECLARE_METATYPE(QVector<LogEntry>)
Q_DECLARE_METATYPE(LogEntry::LogLevel)

#endif // LOGENTRY_H
