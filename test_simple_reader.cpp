#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QTextEdit>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

#include "simple_async_file_reader.h"

/**
 * @brief 简化版异步文件读取器测试窗口
 */
class SimpleReaderTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit SimpleReaderTestWindow(QWidget *parent = nullptr)
        : QMainWindow(parent)
    {
        setupUI();
        setupReader();
    }

private slots:
    void onStartButtonClicked()
    {
        QString fileName = QFileDialog::getOpenFileName(this, 
            "选择文件", "", "所有文件 (*.*)");
        
        if (!fileName.isEmpty()) {
            m_logOutput->clear();
            m_progressBar->setValue(0);
            m_statusLabel->setText("开始读取文件...");
            
            // 开始异步读取，最多读取5000行
            m_reader->startReading(fileName, 5000);
            
            m_startButton->setEnabled(false);
            m_cancelButton->setEnabled(true);
        }
    }

    void onCancelButtonClicked()
    {
        m_reader->cancelReading();
    }

    void onReadingStarted(const QString& filePath, int estimatedLines)
    {
        m_statusLabel->setText(QString("开始读取: %1 (估计 %2 行)")
                              .arg(QFileInfo(filePath).fileName())
                              .arg(estimatedLines));
        m_progressBar->setMaximum(estimatedLines);
        
        qDebug() << "Reading started:" << filePath;
    }

    void onProgressUpdated(int processedLines, int totalLines, int percentage)
    {
        m_progressBar->setValue(processedLines);
        m_statusLabel->setText(QString("处理中: %1/%2 行 (%3%)")
                              .arg(processedLines)
                              .arg(totalLines)
                              .arg(percentage));
    }

    void onDataChunkReady(const QStringList& lines, bool isLastChunk)
    {
        // 显示前几行内容
        for (int i = 0; i < qMin(3, lines.size()); ++i) {
            m_logOutput->append(QString("第%1行: %2").arg(i+1).arg(lines[i]));
        }
        
        if (lines.size() > 3) {
            m_logOutput->append(QString("... 本批次还有 %1 行").arg(lines.size() - 3));
        }
        
        m_logOutput->append("---");
        
        qDebug() << "Data chunk ready:" << lines.size() << "lines, last:" << isLastChunk;
    }

    void onReadingCompleted(int totalLines, qint64 elapsedMs)
    {
        m_statusLabel->setText(QString("读取完成: %1 行，耗时 %2 毫秒")
                              .arg(totalLines)
                              .arg(elapsedMs));
        
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
        
        QMessageBox::information(this, "完成", 
                               QString("成功读取 %1 行\n耗时: %2 毫秒")
                               .arg(totalLines).arg(elapsedMs));
    }

    void onReadingCancelled()
    {
        m_statusLabel->setText("读取已取消");
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
    }

    void onErrorOccurred(const QString& error)
    {
        m_statusLabel->setText("错误: " + error);
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
        
        QMessageBox::critical(this, "错误", error);
    }

private:
    void setupUI()
    {
        setWindowTitle("简化版异步文件读取器测试");
        setMinimumSize(600, 400);

        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);

        // 按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        m_startButton = new QPushButton("选择文件并开始读取", this);
        m_cancelButton = new QPushButton("取消", this);
        m_cancelButton->setEnabled(false);
        
        buttonLayout->addWidget(m_startButton);
        buttonLayout->addWidget(m_cancelButton);
        buttonLayout->addStretch();

        // 状态和进度
        m_statusLabel = new QLabel("准备就绪", this);
        m_progressBar = new QProgressBar(this);

        // 输出
        m_logOutput = new QTextEdit(this);
        m_logOutput->setReadOnly(true);

        mainLayout->addLayout(buttonLayout);
        mainLayout->addWidget(m_statusLabel);
        mainLayout->addWidget(m_progressBar);
        mainLayout->addWidget(m_logOutput);

        // 连接信号
        connect(m_startButton, &QPushButton::clicked, this, &SimpleReaderTestWindow::onStartButtonClicked);
        connect(m_cancelButton, &QPushButton::clicked, this, &SimpleReaderTestWindow::onCancelButtonClicked);
    }

    void setupReader()
    {
        m_reader = new SimpleAsyncFileReader(this);
        
        // 连接信号
        connect(m_reader, &SimpleAsyncFileReader::readingStarted,
                this, &SimpleReaderTestWindow::onReadingStarted);
        connect(m_reader, &SimpleAsyncFileReader::progressUpdated,
                this, &SimpleReaderTestWindow::onProgressUpdated);
        connect(m_reader, &SimpleAsyncFileReader::dataChunkReady,
                this, &SimpleReaderTestWindow::onDataChunkReady);
        connect(m_reader, &SimpleAsyncFileReader::readingCompleted,
                this, &SimpleReaderTestWindow::onReadingCompleted);
        connect(m_reader, &SimpleAsyncFileReader::readingCancelled,
                this, &SimpleReaderTestWindow::onReadingCancelled);
        connect(m_reader, &SimpleAsyncFileReader::errorOccurred,
                this, &SimpleReaderTestWindow::onErrorOccurred);
    }

private:
    SimpleAsyncFileReader* m_reader;
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QTextEdit* m_logOutput;
    QPushButton* m_startButton;
    QPushButton* m_cancelButton;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    qDebug() << "Starting simple async file reader test...";

    SimpleReaderTestWindow window;
    window.show();

    return app.exec();
}

#include "test_simple_reader.moc"
