#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>
#include <QTimer>
#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include "simplelogviewer.h"

/**
 * @brief 数据显示问题修复验证程序
 */
class DataDisplayTestWidget : public QWidget
{
    Q_OBJECT

public:
    DataDisplayTestWidget(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        m_logViewer = nullptr;
    }

private slots:
    void onCreateViewer()
    {
        if (m_logViewer) {
            delete m_logViewer;
        }
        
        m_logViewer = new SimpleLogViewer(SimpleLogViewer::FileViewer, this);
        m_viewerLayout->addWidget(m_logViewer);
        
        // 连接信号
        connect(m_logViewer, &SimpleLogViewer::dataLoaded,
                [this](int count) {
                    m_logOutput->append(QString("✓ 数据加载完成信号: %1 条").arg(count));
                    
                    // 检查视图中的实际行数
                    QTimer::singleShot(200, [this]() {
                        checkViewData();
                    });
                });
        
        connect(m_logViewer, &SimpleLogViewer::errorOccurred,
                [this](const QString& error) {
                    m_logOutput->append(QString("✗ 错误: %1").arg(error));
                });
        
        m_logOutput->append("✓ 文件查看器创建成功");
    }
    
    void onLoadTestFile()
    {
        if (!m_logViewer) {
            m_logOutput->append("✗ 请先创建查看器");
            return;
        }
        
        QString fileName = QFileDialog::getOpenFileName(
            this,
            "选择日志文件",
            "",
            "日志文件 (*.log *.txt);;所有文件 (*.*)"
        );
        
        if (!fileName.isEmpty()) {
            m_logOutput->append(QString("正在加载文件: %1").arg(fileName));
            
            bool success = m_logViewer->loadFile(fileName, "UTF-8");
            if (success) {
                m_logOutput->append("✓ 文件加载请求已发送");
            } else {
                m_logOutput->append("✗ 文件加载失败");
            }
        }
    }
    
    void onCheckData()
    {
        if (!m_logViewer) {
            m_logOutput->append("✗ 请先创建查看器");
            return;
        }
        
        checkViewData();
    }
    
    void checkViewData()
    {
        if (!m_logViewer) {
            return;
        }
        
        // 获取模型中的数据数量
        int modelCount = m_logViewer->getLogCount();
        
        // 获取视图中显示的行数
        // 这里需要访问内部的表格视图，但SimpleLogViewer可能没有公开接口
        // 我们通过状态信息来判断
        QString status = m_logViewer->getStatusInfo();
        
        m_logOutput->append("=== 数据检查结果 ===");
        m_logOutput->append(QString("模型中的数据数量: %1").arg(modelCount));
        m_logOutput->append(QString("当前状态: %1").arg(status));
        
        if (modelCount > 0) {
            m_logOutput->append("✓ 模型中有数据");
            m_logOutput->append("如果表格为空，说明视图更新有问题");
        } else {
            m_logOutput->append("✗ 模型中没有数据");
        }
    }

private:
    void setupUI()
    {
        setWindowTitle("数据显示问题修复测试");
        setMinimumSize(900, 700);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        
        // 控制面板
        QWidget* controlPanel = new QWidget();
        QHBoxLayout* controlLayout = new QHBoxLayout(controlPanel);
        
        QPushButton* createBtn = new QPushButton("创建查看器");
        connect(createBtn, &QPushButton::clicked, this, &DataDisplayTestWidget::onCreateViewer);
        controlLayout->addWidget(createBtn);
        
        QPushButton* loadBtn = new QPushButton("加载文件");
        connect(loadBtn, &QPushButton::clicked, this, &DataDisplayTestWidget::onLoadTestFile);
        controlLayout->addWidget(loadBtn);
        
        QPushButton* checkBtn = new QPushButton("检查数据");
        connect(checkBtn, &QPushButton::clicked, this, &DataDisplayTestWidget::onCheckData);
        controlLayout->addWidget(checkBtn);
        
        controlLayout->addStretch();
        
        mainLayout->addWidget(controlPanel);
        
        // 查看器容器
        QWidget* viewerContainer = new QWidget();
        viewerContainer->setMinimumHeight(400);
        m_viewerLayout = new QVBoxLayout(viewerContainer);
        mainLayout->addWidget(viewerContainer, 3);
        
        // 测试输出
        m_logOutput = new QTextEdit();
        m_logOutput->setMaximumHeight(200);
        m_logOutput->setPlainText("=== 数据显示问题修复测试 ===\n");
        m_logOutput->append("问题描述: SimpleFileDataSource没有发出dataReady信号");
        m_logOutput->append("");
        m_logOutput->append("修复内容:");
        m_logOutput->append("1. SimpleFileDataSource::onAsyncDataChunkReady()发出dataReady信号");
        m_logOutput->append("2. 修复SimpleLogViewer::loadFile()的错误逻辑");
        m_logOutput->append("3. 添加详细调试信息跟踪信号流");
        m_logOutput->append("");
        m_logOutput->append("测试步骤:");
        m_logOutput->append("1. 点击'创建查看器'");
        m_logOutput->append("2. 点击'加载文件'选择日志文件");
        m_logOutput->append("3. 观察表格是否显示数据");
        m_logOutput->append("4. 点击'检查数据'验证数据状态");
        m_logOutput->append("");
        m_logOutput->append("预期结果: 表格应该显示加载的日志数据");
        m_logOutput->append("");
        mainLayout->addWidget(m_logOutput);
    }

private:
    SimpleLogViewer* m_logViewer;
    QVBoxLayout* m_viewerLayout;
    QTextEdit* m_logOutput;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    DataDisplayTestWidget widget;
    widget.show();
    
    return app.exec();
}

#include "test_data_display.moc"
