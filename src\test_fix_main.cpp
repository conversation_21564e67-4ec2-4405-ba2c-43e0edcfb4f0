#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 快速测试修复效果
 */
void quickTest()
{
    qDebug() << "=== 快速修复测试 ===";
    
    // 1. 创建FileLogModel
    FileLogModel sourceModel;
    qDebug() << QString("源模型: rowCount=%1, columnCount=%2")
                .arg(sourceModel.rowCount())
                .arg(sourceModel.columnCount());
    
    // 2. 添加测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 5; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("Source%1").arg(i));
        entry.setMessage(QString("消息 %1").arg(i));
        entry.setDetails(QString("详情 %1").arg(i));
        testEntries.append(entry);
    }
    
    sourceModel.addLogEntries(testEntries);
    qDebug() << QString("添加数据后: rowCount=%1, totalCount=%2")
                .arg(sourceModel.rowCount())
                .arg(sourceModel.getTotalCount());
    
    // 3. 创建代理模型
    LogSortFilterProxyModel proxyModel;
    proxyModel.setSourceModel(&sourceModel);
    
    qDebug() << QString("代理模型: rowCount=%1, columnCount=%2")
                .arg(proxyModel.rowCount())
                .arg(proxyModel.columnCount());
    
    // 4. 检查数据
    if (proxyModel.rowCount() > 0) {
        qDebug() << "✅ 修复成功！代理模型可以显示数据";
        
        // 显示第一行数据
        for (int col = 0; col < proxyModel.columnCount(); ++col) {
            QModelIndex index = proxyModel.index(0, col);
            QVariant data = proxyModel.data(index, Qt::DisplayRole);
            qDebug() << QString("  列 %1: %2").arg(col).arg(data.toString());
        }
    } else {
        qDebug() << "❌ 修复失败！代理模型没有数据";
    }
    
    // 5. 测试过滤器
    qDebug() << "测试文本过滤器...";
    proxyModel.setFilterPattern("消息");
    qDebug() << QString("过滤后: rowCount=%1").arg(proxyModel.rowCount());
    
    proxyModel.setFilterPattern("");
    qDebug() << QString("清除过滤器后: rowCount=%1").arg(proxyModel.rowCount());
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    quickTest();
    
    return 0;
}
