#ifndef STREAMLINED_LOG_APPENDER_H
#define STREAMLINED_LOG_APPENDER_H

#include "logviewer_global.h"
#include "logentry.h"
#include <QObject>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/appenderskeleton.h>
#include <log4qt/loggingevent.h>
#endif

/**
 * @brief 精简的Log4Qt Appender
 *
 * 特点：
 * - 极简实现：只做日志转换和信号发射
 * - 线程安全：使用Qt::QueuedConnection确保跨线程安全
 * - 高性能：无缓存、无复杂处理，直接转发
 * - 兼容性：支持Log4Qt和模拟模式
 */
#ifdef LOG4QT_AVAILABLE
class LOGVIEWER_EXPORT StreamlinedLogAppender : public Log4Qt::AppenderSkeleton
#else
class LOGVIEWER_EXPORT StreamlinedLogAppender : public QObject
#endif
{
    Q_OBJECT

public:
    explicit StreamlinedLogAppender(QObject* parent = nullptr);
    ~StreamlinedLogAppender() override;

#ifdef LOG4QT_AVAILABLE
    // ========== Log4Qt Appender 接口实现 ==========
    
    /**
     * @brief Log4Qt日志事件处理方法
     */
    void append(const Log4Qt::LoggingEvent& event) override;

    /**
     * @brief 激活选项 - Log4Qt框架要求
     */
    void activateOptions() override;

    /**
     * @brief 检查是否需要布局
     */
    bool requiresLayout() const override { return false; }
#endif

    /**
     * @brief 模拟接收日志条目（用于测试）
     */
    void simulateLogEntry(const LogEntry& entry);

signals:
    /**
     * @brief 日志接收信号
     */
    void logReceived(const LogEntry& entry);

private:
#ifdef LOG4QT_AVAILABLE
    /**
     * @brief 将Log4Qt事件转换为LogEntry
     */
    LogEntry convertToLogEntry(const Log4Qt::LoggingEvent& event) const;
    
    /**
     * @brief 转换日志级别
     */
    LogEntry::LogLevel convertLogLevel(int log4qtLevel) const;
#endif

    /**
     * @brief 线程安全的信号发射
     */
    void emitLogReceived(const LogEntry& entry);

private:
    int m_totalCount; // 接收计数
};

#endif // STREAMLINED_LOG_APPENDER_H
