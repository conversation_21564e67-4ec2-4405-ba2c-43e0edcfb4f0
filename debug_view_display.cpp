#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>

#include "filelogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 详细调试数据显示问题的程序
 */
void debugDataFlow()
{
    qDebug() << "=== 详细调试数据显示链路 ===";
    
    // 1. 创建FileLogModel并添加数据
    qDebug() << "\n1. 创建FileLogModel并添加测试数据...";
    FileLogModel sourceModel;
    
    // 创建测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 5; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("TestSource%1").arg(i));
        entry.setMessage(QString("测试消息 %1").arg(i));
        entry.setDetails(QString("详细信息 %1").arg(i));
        testEntries.append(entry);
        
        qDebug() << QString("  创建条目 %1: 级别=%2, 消息=%3")
                    .arg(i)
                    .arg(static_cast<int>(entry.level()))
                    .arg(entry.message());
    }
    
    sourceModel.addLogEntries(testEntries);
    
    qDebug() << QString("源模型状态: rowCount=%1, columnCount=%2, totalCount=%3")
                .arg(sourceModel.rowCount())
                .arg(sourceModel.columnCount())
                .arg(sourceModel.getTotalCount());
    
    // 2. 验证源模型数据
    qDebug() << "\n2. 验证源模型数据访问...";
    for (int row = 0; row < sourceModel.rowCount(); ++row) {
        LogEntry entry = sourceModel.getLogEntry(row);
        qDebug() << QString("  行 %1: 时间=%2, 级别=%3, 消息=%4")
                    .arg(row)
                    .arg(entry.timestamp().toString("hh:mm:ss"))
                    .arg(static_cast<int>(entry.level()))
                    .arg(entry.message());
        
        // 验证data()方法
        for (int col = 0; col < sourceModel.columnCount(); ++col) {
            QModelIndex index = sourceModel.index(row, col);
            QVariant data = sourceModel.data(index, Qt::DisplayRole);
            qDebug() << QString("    列 %1: %2").arg(col).arg(data.toString());
        }
    }
    
    // 3. 创建代理模型
    qDebug() << "\n3. 创建LogSortFilterProxyModel...";
    LogSortFilterProxyModel proxyModel;
    proxyModel.setSourceModel(&sourceModel);
    
    qDebug() << QString("代理模型初始状态: rowCount=%1, columnCount=%2")
                .arg(proxyModel.rowCount())
                .arg(proxyModel.columnCount());
    
    // 4. 检查代理模型的过滤器状态
    qDebug() << "\n4. 检查代理模型过滤器状态...";
    qDebug() << "  filterPattern:" << proxyModel.filterPattern();
    qDebug() << "  caseSensitivity:" << proxyModel.caseSensitivity();
    qDebug() << "  sourceFilter:" << proxyModel.sourceFilter();
    
    // 检查级别过滤器
    QStringList visibleLevels;
    for (int i = 0; i < 5; ++i) {
        LogEntry::LogLevel level = static_cast<LogEntry::LogLevel>(i);
        if (proxyModel.isLevelVisible(level)) {
            visibleLevels << QString::number(i);
        }
    }
    qDebug() << "  可见级别:" << visibleLevels.join(", ");
    
    // 5. 测试代理模型数据访问
    qDebug() << "\n5. 测试代理模型数据访问...";
    int proxyRows = proxyModel.rowCount();
    int proxyCols = proxyModel.columnCount();
    
    if (proxyRows == 0) {
        qWarning() << "  ⚠️ 代理模型没有行数据！这是问题所在！";
        
        // 手动测试filterAcceptsRow
        qDebug() << "\n6. 手动测试filterAcceptsRow...";
        for (int row = 0; row < sourceModel.rowCount(); ++row) {
            // 这里我们需要访问私有方法，所以创建一个测试
            QModelIndex sourceIndex = sourceModel.index(row, 0);
            bool accepted = true; // 我们无法直接调用filterAcceptsRow，但可以检查结果
            
            LogEntry entry = sourceModel.getLogEntry(row);
            qDebug() << QString("  源行 %1: 级别=%2, 消息=%3")
                        .arg(row)
                        .arg(static_cast<int>(entry.level()))
                        .arg(entry.message());
            
            // 检查这个条目是否应该被过滤
            // 检查级别过滤器
            if (!proxyModel.isLevelVisible(entry.level())) {
                qDebug() << QString("    ❌ 级别 %1 被过滤").arg(static_cast<int>(entry.level()));
                accepted = false;
            }
            
            // 检查文本过滤器
            QString filterPattern = proxyModel.filterPattern();
            if (!filterPattern.isEmpty()) {
                bool matchFound = false;
                if (entry.message().contains(filterPattern, Qt::CaseInsensitive) ||
                    entry.details().contains(filterPattern, Qt::CaseInsensitive) ||
                    entry.source().contains(filterPattern, Qt::CaseInsensitive)) {
                    matchFound = true;
                }
                if (!matchFound) {
                    qDebug() << QString("    ❌ 文本过滤器 '%1' 不匹配").arg(filterPattern);
                    accepted = false;
                }
            }
            
            if (accepted) {
                qDebug() << QString("    ✅ 行 %1 应该被接受").arg(row);
            }
        }
    } else {
        qDebug() << QString("  ✅ 代理模型有 %1 行数据").arg(proxyRows);
        
        for (int row = 0; row < qMin(3, proxyRows); ++row) {
            qDebug() << QString("  代理行 %1:").arg(row);
            for (int col = 0; col < proxyCols; ++col) {
                QModelIndex proxyIndex = proxyModel.index(row, col);
                QVariant data = proxyModel.data(proxyIndex, Qt::DisplayRole);
                qDebug() << QString("    列 %1: %2").arg(col).arg(data.toString());
            }
        }
    }
    
    // 7. 测试清除过滤器
    qDebug() << "\n7. 测试清除所有过滤器...";
    proxyModel.setFilterPattern("");
    proxyModel.setSourceFilter("");
    
    // 确保所有级别都可见
    for (int i = 0; i < 5; ++i) {
        LogEntry::LogLevel level = static_cast<LogEntry::LogLevel>(i);
        proxyModel.setLevelFilter(level, true);
    }
    
    qDebug() << QString("清除过滤器后: rowCount=%1, columnCount=%2")
                .arg(proxyModel.rowCount())
                .arg(proxyModel.columnCount());
    
    if (proxyModel.rowCount() > 0) {
        qDebug() << "  ✅ 清除过滤器后有数据显示";
    } else {
        qDebug() << "  ❌ 清除过滤器后仍然没有数据显示";
    }
}

void debugLogEntryData()
{
    qDebug() << "\n=== 调试LogEntry数据完整性 ===";
    
    LogEntry entry;
    entry.setTimestamp(QDateTime::currentDateTime());
    entry.setLevel(LogEntry::LogLevel::Info);
    entry.setSource("TestSource");
    entry.setMessage("测试消息");
    entry.setDetails("测试详情");
    
    qDebug() << "LogEntry数据:";
    qDebug() << "  timestamp():" << entry.timestamp();
    qDebug() << "  level():" << static_cast<int>(entry.level());
    qDebug() << "  levelString():" << entry.levelString();
    qDebug() << "  source():" << entry.source();
    qDebug() << "  message():" << entry.message();
    qDebug() << "  details():" << entry.details();
    
    // 测试空LogEntry
    LogEntry emptyEntry;
    qDebug() << "空LogEntry数据:";
    qDebug() << "  timestamp().isValid():" << emptyEntry.timestamp().isValid();
    qDebug() << "  level():" << static_cast<int>(emptyEntry.level());
    qDebug() << "  message().isEmpty():" << emptyEntry.message().isEmpty();
}

void debugColumnVisibility()
{
    qDebug() << "\n=== 调试列可见性 ===";
    
    FileLogModel model;
    
    qDebug() << "默认列可见性:";
    for (int i = 0; i < static_cast<int>(BaseLogModel::ColumnCount); ++i) {
        BaseLogModel::Column col = static_cast<BaseLogModel::Column>(i);
        bool visible = model.isColumnVisible(col);
        QString colName = model.getColumnName(i);
        qDebug() << QString("  列 %1 (%2): %3").arg(i).arg(colName).arg(visible ? "可见" : "隐藏");
    }
    
    qDebug() << "columnCount():" << model.columnCount();
    
    // 测试列映射
    qDebug() << "列映射测试:";
    for (int visibleCol = 0; visibleCol < model.columnCount(); ++visibleCol) {
        int actualCol = model.mapToActualColumn(visibleCol);
        QString colName = model.getColumnName(actualCol);
        qDebug() << QString("  可见列 %1 -> 实际列 %2 (%3)").arg(visibleCol).arg(actualCol).arg(colName);
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 数据显示问题详细调试程序 ===";
    
    debugLogEntryData();
    debugColumnVisibility();
    debugDataFlow();
    
    qDebug() << "\n=== 调试完成 ===";
    
    return 0;
}
