#include "circularlogmodel.h"
#include "simpleconfigmanager.h"
#include <QColor>
#include <QBrush>
#include <QFont>
#include <QSize>
#include <QDebug>

CircularLogModel::CircularLogModel(int capacity, QObject* parent)
    : BaseLogModel(parent)
    , m_entries(capacity > 0 ? capacity : 20000)
    , m_maxEntries(capacity > 0 ? static_cast<uint64_t>(capacity) : 20000)
    , m_pendingUIUpdate(false)
    , m_lastUpdateSize(0)
{
    // 从配置管理器获取最大条目数（如果没有指定容量）
    if (capacity <= 0) {
        SimpleConfigManager& config = SimpleConfigManager::instance();
        m_maxEntries = static_cast<uint64_t>(config.getMaxLogEntries());
        m_entries.setCapacity(static_cast<int>(m_maxEntries));
    }

    // 初始化UI更新定时器
    m_uiUpdateTimer = new QTimer(this);
    m_uiUpdateTimer->setSingleShot(true);
    m_uiUpdateTimer->setInterval(100); // 100ms延迟，平衡响应性和性能
    connect(m_uiUpdateTimer, &QTimer::timeout, this, &CircularLogModel::performScheduledUIUpdate);

    qDebug() << "CircularLogModel initialized with capacity:" << m_entries.capacity();
}

CircularLogModel::~CircularLogModel()
{
    qDebug() << "CircularLogModel destroyed";
}

int CircularLogModel::rowCount(const QModelIndex& parent) const
{
    if (parent.isValid())
        return 0;
    
    QMutexLocker locker(&m_mutex);
    return m_entries.size();
}

QVariant CircularLogModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid())
        return QVariant();

    LogEntry entry;
    int actualColumn = -1;

    {
        QMutexLocker locker(&m_mutex);
        if (index.row() >= m_entries.size() || index.row() < 0)
            return QVariant();

        entry = m_entries.at(index.row());
        actualColumn = mapToActualColumn(index.column());
    }

    if (actualColumn < 0 || actualColumn >= static_cast<int>(ColumnCount))
        return QVariant();

    return formatCircularLogEntryData(entry, actualColumn, role);
}

void CircularLogModel::addLogEntry(const LogEntry& entry)
{
    try {
        QMutexLocker locker(&m_mutex);

        int currentSize = m_entries.size();
        bool isFull = m_entries.isFull();
        bool isNearFull = (currentSize >= m_entries.capacity() * 0.9);

        locker.unlock();

        // 检查是否需要内存清理
        if (m_entries.needsMemoryCleanup()) {
            m_entries.forceMemoryCleanup();
            emit memoryWarning();
        }

        // 优化的UI更新策略
        if (isFull || isNearFull) {
            locker.relock();
            m_entries.append(entry);
            locker.unlock();
            scheduleUIUpdate();
        } else {
            beginInsertRows(QModelIndex(), currentSize, currentSize);
            locker.relock();
            m_entries.append(entry);
            locker.unlock();
            endInsertRows();
        }
    }
    catch (const std::exception& e) {
        qWarning() << "CircularLogModel: 添加日志条目时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "CircularLogModel: 添加日志条目时发生未知异常";
    }
}

void CircularLogModel::addLogEntries(const QVector<LogEntry>& entries)
{
    if (entries.isEmpty()) {
        return;
    }

    try {
        // 检查是否需要内存清理
        if (m_entries.needsMemoryCleanup()) {
            m_entries.forceMemoryCleanup();
            emit memoryWarning();
        }

        QMutexLocker locker(&m_mutex);
        int currentSize = m_entries.size();
        locker.unlock();

        // 根据数据量选择更新方式
        if (shouldUseDelayedUpdate(entries.size())) {
            locker.relock();
            int oldSize = m_entries.size();
            m_entries.append(entries);
            int newSize = m_entries.size();
            int actualAdded = newSize - oldSize;
            locker.unlock();

            if (actualAdded < entries.size()) {
                qWarning() << "CircularLogModel: 数据丢失警告! 尝试添加" << entries.size()
                          << "条，实际添加" << actualAdded << "条";
            }
            scheduleUIUpdate();
        } else {
            int lastRow = currentSize + entries.size() - 1;
            beginInsertRows(QModelIndex(), currentSize, lastRow);
            locker.relock();
            m_entries.append(entries);
            locker.unlock();
            endInsertRows();
        }
    }
    catch (const std::exception& e) {
        qWarning() << "CircularLogModel: 处理日志条目时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "CircularLogModel: 处理日志条目时发生未知异常";
    }
}

LogEntry CircularLogModel::getLogEntry(int row) const
{
    QMutexLocker locker(&m_mutex);
    return (row >= 0 && row < m_entries.size()) ? m_entries.at(row) : LogEntry();
}

QVector<LogEntry> CircularLogModel::getEntries(int startIndex, int count) const
{
    QMutexLocker locker(&m_mutex);
    return m_entries.mid(startIndex, count);
}

void CircularLogModel::clear()
{
    try {
        qDebug() << "CircularLogModel::clear() 开始执行";

        m_lastUpdateSize = 0;

        beginResetModel();
        {
            QMutexLocker locker(&m_mutex);
            int oldSize = m_entries.size();
            m_entries.clear();
            qDebug() << "CircularLogModel: 清除了" << oldSize << "条数据";
        }
        endResetModel();

        // 内存优化
        {
            QMutexLocker locker(&m_mutex);
            m_entries.squeeze();
        }
        qDebug() << "CircularLogModel::clear() 执行完成";
    }
    catch (const std::exception& e) {
        qWarning() << "CircularLogModel: 清除日志时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "CircularLogModel: 清除日志时发生未知异常";
    }
}

qint64 CircularLogModel::getMemoryUsage() const
{
    QMutexLocker locker(&m_mutex);
    return m_entries.memoryUsage();
}

int CircularLogModel::getTotalCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_entries.size();
}

int CircularLogModel::getCapacity() const
{
    QMutexLocker locker(&m_mutex);
    return m_entries.capacity();
}

void CircularLogModel::setCapacity(int capacity)
{
    QMutexLocker locker(&m_mutex);
    m_entries.setCapacity(capacity);
    m_maxEntries = static_cast<uint64_t>(capacity);
}

void CircularLogModel::setMaxEntries(uint64_t maxEntries)
{
    setCapacity(static_cast<int>(maxEntries));
}

bool CircularLogModel::needsMemoryCleanup() const
{
    return m_entries.needsMemoryCleanup();
}

void CircularLogModel::forceMemoryCleanup()
{
    m_entries.forceMemoryCleanup();
    emit memoryWarning();
}

double CircularLogModel::getUsageRatio() const
{
    QMutexLocker locker(&m_mutex);
    int capacity = m_entries.capacity();
    if (capacity == 0) return 0.0;
    return static_cast<double>(m_entries.size()) / capacity;
}

bool CircularLogModel::isFull() const
{
    QMutexLocker locker(&m_mutex);
    return m_entries.isFull();
}

void CircularLogModel::removeEntriesAt(int startIndex, int count)
{
    Q_UNUSED(startIndex)
    Q_UNUSED(count)
    qWarning() << "CircularLogModel: 环形缓冲区不支持任意位置删除操作";
}

void CircularLogModel::retainDataRange(int startIndex, int endIndex, int marginCount)
{
    Q_UNUSED(startIndex)
    Q_UNUSED(endIndex)
    Q_UNUSED(marginCount)
    qWarning() << "CircularLogModel: 环形缓冲区不支持保留范围操作";
}

void CircularLogModel::scheduleUIUpdate()
{
    if (!m_pendingUIUpdate) {
        m_pendingUIUpdate = true;
        m_uiUpdateTimer->start();
    }
}

void CircularLogModel::performScheduledUIUpdate()
{
    if (!m_pendingUIUpdate) {
        return;
    }

    try {
        QMutexLocker locker(&m_mutex);
        int currentSize = m_entries.size();
        locker.unlock();

        if (currentSize != m_lastUpdateSize) {
            beginResetModel();
            endResetModel();
            m_lastUpdateSize = currentSize;
            qDebug() << "CircularLogModel: 执行延迟UI更新，当前大小:" << currentSize;
        }

        m_pendingUIUpdate = false;
    }
    catch (const std::exception& e) {
        qWarning() << "CircularLogModel: 执行UI更新时发生异常:" << e.what();
        m_pendingUIUpdate = false;
    }
}

bool CircularLogModel::shouldUseDelayedUpdate(int newEntriesCount) const
{
    return newEntriesCount > 10; // 超过10条使用延迟更新
}

QVariant CircularLogModel::formatCircularLogEntryData(const LogEntry& entry, int column, int role) const
{
    switch (role) {
        case Qt::DisplayRole:
            switch (column) {
                case TimestampColumn:
                    return entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz");
                case LevelColumn:
                    return entry.levelString();
                case SourceColumn:
                    return entry.source();
                case MessageColumn:
                    return entry.message();
                case DetailsColumn:
                    return entry.details();
                default:
                    return QVariant();
            }

        case Qt::BackgroundRole:
            switch (entry.level()) {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(240, 240, 240));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(255, 255, 255));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(255, 255, 200));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(255, 200, 200));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(255, 150, 150));
                default:
                    return QVariant();
            }

        case Qt::ForegroundRole:
            switch (entry.level()) {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(100, 100, 100));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(0, 0, 0));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(150, 100, 0));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(200, 0, 0));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(150, 0, 0));
                default:
                    return QVariant();
            }

        case Qt::FontRole: {
            QFont font;
            if (entry.level() == LogEntry::LogLevel::Critical ||
                entry.level() == LogEntry::LogLevel::Error) {
                font.setBold(true);
            }
            return font;
        }

        case Qt::TextAlignmentRole:
            switch (column) {
                case TimestampColumn:
                case LevelColumn:
                    return Qt::AlignCenter;
                default:
                    return QVariant(Qt::AlignLeft | Qt::AlignVCenter);
            }

        case Qt::ToolTipRole:
            return QString("时间: %1\n级别: %2\n来源: %3\n消息: %4\n详情: %5")
                   .arg(entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                   .arg(entry.levelString())
                   .arg(entry.source())
                   .arg(entry.message())
                   .arg(entry.details());

        default:
            return QVariant();
    }
}
