#include <QCoreApplication>
#include <QDebug>
#include <QDateTime>

#include "filelogmodel.h"
#include "circularlogmodel.h"
#include "logsortfilterproxymodel.h"
#include "logentry.h"

/**
 * @brief 测试代理模型修复的程序
 */
void testProxyModelWithFileLogModel()
{
    qDebug() << "=== 测试 LogSortFilterProxyModel + FileLogModel ===";
    
    // 创建FileLogModel
    FileLogModel sourceModel;
    
    // 创建代理模型
    LogSortFilterProxyModel proxyModel;
    proxyModel.setSourceModel(&sourceModel);
    
    qDebug() << "初始状态:";
    qDebug() << "  源模型 rowCount():" << sourceModel.rowCount();
    qDebug() << "  源模型 columnCount():" << sourceModel.columnCount();
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
    
    // 创建测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 3; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("TestSource%1").arg(i));
        entry.setMessage(QString("测试消息 %1").arg(i));
        entry.setDetails(QString("详细信息 %1").arg(i));
        testEntries.append(entry);
    }
    
    // 添加数据到源模型
    qDebug() << "添加" << testEntries.size() << "条测试数据...";
    sourceModel.addLogEntries(testEntries);
    
    qDebug() << "添加后状态:";
    qDebug() << "  源模型 rowCount():" << sourceModel.rowCount();
    qDebug() << "  源模型 columnCount():" << sourceModel.columnCount();
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
    
    // 测试代理模型数据访问
    qDebug() << "测试代理模型数据访问:";
    int proxyRows = proxyModel.rowCount();
    int proxyCols = proxyModel.columnCount();
    
    for (int row = 0; row < proxyRows; ++row) {
        qDebug() << "  代理模型行" << row << ":";
        for (int col = 0; col < proxyCols; ++col) {
            QModelIndex proxyIndex = proxyModel.index(row, col);
            QVariant data = proxyModel.data(proxyIndex, Qt::DisplayRole);
            qDebug() << "    列" << col << ":" << data.toString();
        }
    }
    
    // 测试过滤功能
    qDebug() << "测试过滤功能 - 设置文本过滤器 '测试'...";
    proxyModel.setFilterPattern("测试");
    
    qDebug() << "过滤后状态:";
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
    
    // 清除过滤器
    qDebug() << "清除过滤器...";
    proxyModel.setFilterPattern("");
    
    qDebug() << "清除过滤器后状态:";
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
}

void testProxyModelWithCircularLogModel()
{
    qDebug() << "\n=== 测试 LogSortFilterProxyModel + CircularLogModel ===";
    
    // 创建CircularLogModel
    CircularLogModel sourceModel;
    
    // 创建代理模型
    LogSortFilterProxyModel proxyModel;
    proxyModel.setSourceModel(&sourceModel);
    
    qDebug() << "初始状态:";
    qDebug() << "  源模型 rowCount():" << sourceModel.rowCount();
    qDebug() << "  源模型 columnCount():" << sourceModel.columnCount();
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
    
    // 创建测试数据
    QVector<LogEntry> testEntries;
    for (int i = 0; i < 2; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(static_cast<LogEntry::LogLevel>(i % 5));
        entry.setSource(QString("CircularSource%1").arg(i));
        entry.setMessage(QString("环形缓冲区消息 %1").arg(i));
        entry.setDetails(QString("环形缓冲区详情 %1").arg(i));
        testEntries.append(entry);
    }
    
    // 添加数据到源模型
    qDebug() << "添加" << testEntries.size() << "条测试数据...";
    sourceModel.addLogEntries(testEntries);
    
    qDebug() << "添加后状态:";
    qDebug() << "  源模型 rowCount():" << sourceModel.rowCount();
    qDebug() << "  源模型 columnCount():" << sourceModel.columnCount();
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    qDebug() << "  代理模型 columnCount():" << proxyModel.columnCount();
    
    // 测试代理模型数据访问
    qDebug() << "测试代理模型数据访问:";
    int proxyRows = proxyModel.rowCount();
    int proxyCols = proxyModel.columnCount();
    
    for (int row = 0; row < proxyRows; ++row) {
        qDebug() << "  代理模型行" << row << ":";
        for (int col = 0; col < proxyCols; ++col) {
            QModelIndex proxyIndex = proxyModel.index(row, col);
            QVariant data = proxyModel.data(proxyIndex, Qt::DisplayRole);
            qDebug() << "    列" << col << ":" << data.toString();
        }
    }
}

void testLevelFiltering()
{
    qDebug() << "\n=== 测试日志级别过滤 ===";
    
    FileLogModel sourceModel;
    LogSortFilterProxyModel proxyModel;
    proxyModel.setSourceModel(&sourceModel);
    
    // 创建不同级别的测试数据
    QVector<LogEntry> testEntries;
    LogEntry::LogLevel levels[] = {
        LogEntry::LogLevel::Debug,
        LogEntry::LogLevel::Info,
        LogEntry::LogLevel::Warning,
        LogEntry::LogLevel::Error,
        LogEntry::LogLevel::Critical
    };
    
    for (int i = 0; i < 5; ++i) {
        LogEntry entry;
        entry.setTimestamp(QDateTime::currentDateTime().addSecs(i));
        entry.setLevel(levels[i]);
        entry.setSource("TestSource");
        entry.setMessage(QString("级别测试消息 %1").arg(i));
        entry.setDetails("测试详情");
        testEntries.append(entry);
    }
    
    sourceModel.addLogEntries(testEntries);
    
    qDebug() << "所有级别可见时:";
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    
    // 只显示Error和Critical级别
    qDebug() << "设置只显示Error和Critical级别...";
    proxyModel.setLevelFilter(LogEntry::LogLevel::Debug, false);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Info, false);
    proxyModel.setLevelFilter(LogEntry::LogLevel::Warning, false);
    
    qDebug() << "过滤后:";
    qDebug() << "  代理模型 rowCount():" << proxyModel.rowCount();
    
    // 显示过滤后的数据
    int proxyRows = proxyModel.rowCount();
    int proxyCols = proxyModel.columnCount();
    
    for (int row = 0; row < proxyRows; ++row) {
        QModelIndex proxyIndex = proxyModel.index(row, 1); // 级别列
        QVariant data = proxyModel.data(proxyIndex, Qt::DisplayRole);
        qDebug() << "  过滤后行" << row << "级别:" << data.toString();
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 代理模型修复测试程序 ===";
    
    testProxyModelWithFileLogModel();
    testProxyModelWithCircularLogModel();
    testLevelFiltering();
    
    qDebug() << "\n=== 测试完成 ===";
    
    return 0;
}
